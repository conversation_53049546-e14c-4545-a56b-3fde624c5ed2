import Vue from "vue";
import App from "./App";
import store from "./store";
import "static/iconFont.css";
import myHeaderBar from "./components/common/myHeaderBar";
import myTabBar from "./components/common/myTabBar";
import groupx from "@/api/api-groupx";
import myStorage from "@/common/my-storage";

Vue.config.productionTip = false;
Vue.prototype.$store = store;
Vue.prototype.$userInfo = {};
Vue.prototype.$groupx = groupx;
Vue.prototype.$session = {};

Vue.component("top-bar", myHeaderBar);
Vue.component("bottom-tab-bar", myTabBar);
// 设置全局混入，将 Vue.prototype 注入到所有 Vue 实例中
Vue.mixin({
  created() {
    // 在这里可以访问 Vue.prototype 中的属性或方法
    this.$userInfo = myStorage.getUserInfo();
    //console.log(this.$userInfo);
  },
});
App.mpType = "app";

const app = new Vue({
  store,
  ...App,
});
app.$mount();

if (process.env.NODE_ENV === "development") {
  console.log("开发环境");
} else {
  console.log("生产环境");
}
