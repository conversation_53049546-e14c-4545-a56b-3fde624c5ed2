<template>
  <view class="content" @touchstart="handleTouchStart" @touchend="handleTouchEnd" @touchmove.prevent>
    <image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
    <view class="logo-puke-container">
      <logo />
    </view>

    <view class="sel-content">
      <view class="combox-group" @click="showpop('city')">
        <view class="text2">{{ selCityText || '城市' }}</view>
        <view class="arrow-bottom" />
      </view>
      <view class="combox-group" @click="showpop('gender')">
        <view class="text2">{{ selGender || '性别' }}</view>
        <view class="arrow-bottom" />
      </view>
      <view class="combox-group" @click="showpop('era')">
        <text class="text2">{{ selEraText || '年龄' }}</text>
        <view class="arrow-bottom" />
      </view>
    </view>

    <view class="puke-container"
      :style="{ transform: `rotate(${startAngle}deg)`, '--rotation': rotation, '--rotationS': rotationS }"
      :class="{ 'rotateAnimation': animate }" @animationend="resetAnimate">
      <view class="card" v-for="(card, index) in cards" :key="index"
        :style="{ transform: 'rotate(' + (index * angle) + 'deg) translateY(' + (card.translateY + radius) + 'px) translateX(' + (index * gap) + 'px)' }">

        <image :src="card.image" class="card-image" @click="moveUp(index)"></image>

      </view>

    </view>
    <view class="prompt-container">
      <img class="prompt-arc" src="/static/images/arc.png" />
      <view class="prompt-text">
        左右滑动
      </view>
    </view>
    <view class="re-times-container">
      <view class="remaining-times">
        <text class="text-times">剩余联系次数：{{ balanceContacts }}</text>
        <view class="arrow-right" />
      </view>
    </view>

    <bottom-tab-bar :current="0" backgroundColor="#ffffff07" color="#c3daf1" tintColor="#02f32b" @click="tabClick" />

    <popup ref="popup" @submit-event="handlePopupSubmit" style="z-index: 999;"/>
    <cardFriend ref="friend" style="margin-top: -300px;" :user="userInfo" :friend="curFriend"
      @consume-contacts="consumeReturn" />
  </view>
</template>

<script>
import logo from '@/components/common/logo'
import popup from '@/components/comboxPopup/comboxPopup'
import cardFriend from '@/components/cardFriend/cardFriend'
import myStorage from '../../common/my-storage';
import groupx from '../../api/api-groupx';

export default {
  computed: {
    getSelEraText() {

      return this.selEraText ? `${this.selEraText}后` : undefined
    }
  },
  onReady() {
    this.userInfo = myStorage.getUserInfo()
    console.log('puke onReady userInfo:', this.userInfo)
    if (this.userInfo?.account) {
      const vivid = this.userInfo?.vivid
      if (vivid) {
        console.log('puke onReady vivid:', vivid)
        this.selEraText = vivid?.longFor?.era?.toString().padStart(2, '0')
        this.selGender = vivid?.longFor?.gender
        this.selCityText = vivid?.longFor?.city

        this.balanceContacts = vivid?.balanceContacts
        this.startMatch(vivid, vivid?.longFor)
      }
    } else {
      this.getLogin()
    }
  },
  components: { logo, popup, cardFriend },
  data() {
    return {
      selCityText: '', selGender: '', selEraText: '',
      balanceContacts: 0,
      userInfo: myStorage.getUserInfo(),
      loadding: true,
      bg_img: 'https://vivid-public.oss-cn-beijing.aliyuncs.com/bg/bg-1.jpg',
      cards: [
        { image: '/static/puke-back2.png', translateY: 0 }, // 添加 translateY 属性来记录图片的垂直偏移
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 },
        { image: '/static/puke-back2.png', translateY: 0 }
      ],
      startAngle: 0, // 启动角
      radius: 266, // 环形半径
      gap: 0, // 图片间距
      angle: 90 / 11, // 每张图片选择角度
      animate: false,

      touchStartX: 0,
      deltaX: 0,
      rotation: '0deg',
      rotationS: '0deg',
      lastEndRotation: 0,

      friends: {},
      curFriend: {}
    };
  },

  methods: {
    // 消费返回结果
    consumeReturn(res) {

      this.friends = this.friends.filter(item => item.account !== res.receiver)
      this.balanceContacts -= 1
      console.log("消耗一次联系TA", res, this.friends)
    },
    getLogin() {
      uni.showLoading({ title: '登录中...' })
      const that = this
      console.log("getLogin---------")
      uni.login({
        "provider": "weixin",
        "onlyAuthorize": true, // 微信登录仅请求授权认证
        success: function (res) {
          // 登录成功
          uni.getUserInfo({
            provider: 'weixin',
            success: function (info) {
              console.log("getUserInfo---------success", info)
              return
            }
          })
          console.log("getLogin---------success", res)

          if (res.code) {
            that.$groupx.wxLoginVivid(res.code)?.then(result => {
              const userInfo = result.userInfo
              const res = result.res
              console.log("groupx wxLogin---------success", userInfo, res)
              if (userInfo.account) {
                that.balanceContacts = userInfo?.vivid?.balanceContacts
                that.userInfo = myStorage.setUserInfo(userInfo)
                myStorage.setSession({
                  sessionId: res.sessionId,
                  expiresAt: res.expiresAt,
                });
                const vivid = userInfo.vivid
                if (!vivid.longFor) {
                  uni.showModal({
                    showCancel: false,
                    title: '发生错误',
                    content: '您的匹配信息不完整，请先设置.',
                    complete: function (res) { uni.reLaunch({ url: '/pages/index/index' }) }
                  })
                }
                that.startMatch(vivid, vivid.longFor)
              }
              uni.hideLoading();
            }).catch(err => {
              uni.hideLoading();
              console.log("err:", err)
            })
          } else {
            console.log('获取用户登录态失败！' + res.errMsg);
            uni.hideLoading();
          }
        },
        fail: function () {
          uni.hideLoading();
          // uni.showToast({
          //   title: '微信登录失败',
          //   icon: 'none'
          // })
        }
      });
    },
    tabClick(e) {
      console.log("tabClick:", e)
      if (e === 3) {
        uni.reLaunch({
          url: '/pages/mine/home'
        })
      }
    },
    // 弹出地址选择器
    showpop(e) {
      console.log("this:", this)
      this.$refs.popup.showPopup(e);
    },
    // 启动匹配操作
    startMatch(myConditions, myLongFor) {
      const that = this
      uni.showLoading({ title: '匹配中...', mask: true })
      groupx.matchFriends(myConditions, myLongFor)?.then(res => {
        uni.hideLoading()
        console.log("matchFriends:", res)
        if (!res || res.length < 1) { that.showModalError("未能匹配到有效用户，请重新登录。") }
        else {
          that.friends = res
          uni.showToast({ title: '匹配成功', duration: 2000, icon: 'success' })
        }
      }).catch(
        err => {
          uni.hideLoading()
          console.log("matchFriends err:", err)
          that.showModalError("神秘力量导致匹配失败，请稍后再试。")
        }
      )
    },
    // 各种选择器点击保存
    handlePopupSubmit(e) {
      const that = this
      const myConditions = this.userInfo?.vivid
      const user = this.userInfo
      let longFor = user.vivid?.longFor
      if (e.type === 'gender') {
        this.selGender = e.value;
        user.vivid.longFor = { ...longFor, gender: e.value }
      }
      else if (e.type === 'city') { this.selCityText = e.city; user.vivid.longFor = { ...longFor, ...e } }
      else if (e.type === 'era') {
        this.selEraText = e.text;
        user.vivid.longFor = { ...longFor, era: e.num }
      }
      longFor = user.vivid?.longFor
      myStorage.setUserInfo(user)

      // 同步到服务器
      groupx.updateUser(user.vivid)

      console.log("handlePopupSubmit:", e, myConditions, longFor)
      this.startMatch(myConditions, longFor)
    },

    // 弹出好友卡片信息
    showFriend(e) {
      console.log("showFriend:", e)
      this.$refs.friend.showPopup(e);
    },
    showModalError(content) {
      uni.showModal({
        showCancel: false,
        title: "匹配失败",
        content,
        complete: function (res) { uni.reLaunch({ url: '/pages/puke/index' }) }
      })
    },

    moveUp(index) {
      if (this.cards[index].translateY) {
        // 展示TA的信息
        console.log("moveUp-1:", index, this.friends,)
        if (this.balanceContacts < 1) {
          uni.showToast({
            title: "剩余联系次数不足",
            icon: "none",
            duration: 2000
          });
          return
        }

        console.log('moveUp:', index)
        this.showFriend(index)
        this.cards[index].translateY = 0;
        this.cards[index].image = "/static/puke-back2.png";
        return
      }
      // 抽牌,同时其他牌收回.
      const cur = this.friends[index % this.friends.length]
      console.log("moveUp-2:", index % this.friends.length, cur)
      if (!this.friends || this.friends.length < 1 || !cur || !cur.account) {
        uni.showToast({
          title: "未匹配到好友,重新匹配...",
          icon: "none"
        })
        const vivid = this.userInfo.vivid
        this.startMatch(vivid, vivid.longFor)
        return
      }
      groupx.getVividUser(cur.account).then(res => {
        console.log("getVividUser:", cur.account, res)
        if (res.vivid) {
          this.curFriend = res.vivid
          //this.showModalError("对方用户匹配信息不完整，重新匹配")
          // return
        }

      })

      this.cards?.forEach((card, i) => {
        card.translateY = 0;
        card.image = "/static/puke-back2.png";
        if (i == index) {
          card.translateY = 60;
          card.image = "/static/puke-back1.png";
        }
      })
      // this.cards[index].translateY = (this.cards[index].translateY) ? 0 : 60;
      // this.cards[index].image = (this.cards[index].translateY) ? "/static/puke-back1.png" : "/static/puke-back2.png";
    },
    resetAnimate() {
      console.log('resetAnimate')
      // 在动画结束后重置 animate 变量
      //this.animate = false;
      // 延迟将 rotateAnimation 设置为 false，以防止页面跳动

      this.startAngle = this.lastEndRotation % 360
      console.log('startAngle:', this.lastEndRotation)
      this.animate = false;

    },

    handleTouchStart(event) {
      // 记录触摸起始位置
      this.touchStartX = event.touches[0].clientX;
      console.log('touchStartX', this.touchStartX)
    },

    handleTouchEnd(event) {
      // 计算触摸结束位置与起始位置的差值
      const touchEndX = event.changedTouches[0].clientX;
      const deltaX = touchEndX - this.touchStartX;
      if (Math.abs(deltaX) < 6 || this.animate) return

      console.log('deltaX', deltaX, this.animate)
      const deltaxAngle = deltaX * 0.6;
      console.log('deltaxAngle', deltaxAngle)
      const sAngle = (this.startAngle + deltaxAngle);
      this.lastEndRotation = sAngle
      this.rotation = `${this.lastEndRotation}deg`
      this.rotationS = `${this.startAngle}deg`

      this.animate = true;

      console.log('rotation:', this.rotation)
      console.log('rotationS:', this.rotationS)
    }
  }
};

</script>

<style scoped>
body {
  overflow: hidden;
}

.page {
  overflow: hidden;
}

.content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.puke-container {
  position: fixed;
  widows: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: red;
  
  top: 52.6%;
  left: 50%
}

.card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform-origin: top left;
}

.card-image {
  width: 100px;
  /* Adjust width and height according to your card size */
  height: 150px;
}

/* 定义旋转动画 */
.rotateAnimation {
  animation: rotate 2.61s ease;
  /* transform-origin: 50% 50%; */
  animation-fill-mode: forwards;
  /* 将旋转中心设置为元素中心 */
}

@keyframes rotate {
  0% {
    transform: rotate(--rotationS);
    /* transform: rotate(--rotationS);  */
  }

  100% {
    /* transform: rotate(300deg);   */
    transform: rotate(var(--rotation));
    /* transform: rotate(var(--rotationS,0deg)); */
  }
}

.background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.logo-puke-container {
  margin-top: 180rpx;
  z-index: -1;
}

.sel-content {

  margin: 40rpx;
  height: 48px;

  display: flex;
  flex-direction: row;
  justify-content: space-between;

  opacity: 0.8;
}

.combox-group {
  width: 168rpx;
  height: 48rpx;
  line-height: 48rpx;
  background: rgba(213, 228, 255, 0.2);
  border-radius: 8rpx 8rpx 8rpx 8rpx;

  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: center;
  align-content: center;
  justify-content: center;
  padding: 4rpx 20rpx;
}

.text2 {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #D5E4FF;
  font-style: normal;
  text-transform: none;
  text-align: center;
  /* 水平居中 */
}


.arrow-bottom {
  margin-left: 8rpx;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  /* 左边框的宽 */
  border-right: 5px solid transparent;
  /* 右边框的宽 */
  border-top: 7px solid #D5E4FF;
  /* 下边框的长度|高,以及背景色 */
  font-size: 0;
  line-height: 0;
}

.prompt-container {

  margin-top: 99%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.prompt-arc {
  width: 210rpx;
  height: 24rpx;

}

.prompt-text {
  width: 96rpx;
  height: 34rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #D5E4FF;
  line-height: 28rpx;
  text-align: center;
  font-style: normal;
  text-transform: none;
  opacity: 0.4;
}

.re-times-container {
  margin-top: 46rpx;
  display: flex;
  justify-content: center;
}

.remaining-times {
  align-self: center;
  height: 56rpx;
  line-height: 48rpx;
  background: rgba(213, 228, 255, 0.2);
  border-radius: 8rpx 8rpx 8rpx 8rpx;

  display: flex;
  flex-direction: row;
  align-items: center;
  text-align: center;
  align-content: center;
  justify-content: center;
  padding: 4rpx 20rpx;
}

.text-times {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #D5E4FF;
  line-height: 28rpx;
  text-align: left;
  font-style: normal;
  text-transform: none;
}

.arrow-right {
  margin-left: 80rpx;
  width: 0;
  height: 0;
  border-top: 5px solid transparent;
  border-bottom: 5px solid transparent;
  border-left: 7px solid #D5E4FF;
  font-size: 0;
  line-height: 0;
}
</style>
