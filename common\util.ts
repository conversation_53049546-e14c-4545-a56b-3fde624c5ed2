function friendlyDate(timestamp: number): string {
  const formats: Record<string, string> = {
    year: "%n% 年前",
    month: "%n% 月前",
    day: "%n% 天前",
    hour: "%n% 小时前",
    minute: "%n% 分钟前",
    second: "%n% 秒前",
  }

  const now = Date.now()
  const seconds = Math.floor((now - timestamp) / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)
  const months = Math.floor(days / 30)
  const years = Math.floor(months / 12)

  let diffType = ""
  let diffValue = 0

  if (years > 0) {
    diffType = "year"
    diffValue = years
  } else if (months > 0) {
    diffType = "month"
    diffValue = months
  } else if (days > 0) {
    diffType = "day"
    diffValue = days
  } else if (hours > 0) {
    diffType = "hour"
    diffValue = hours
  } else if (minutes > 0) {
    diffType = "minute"
    diffValue = minutes
  } else {
    diffType = "second"
    diffValue = seconds === 0 ? 1 : seconds
  }

  return formats[diffType].replace("%n%", diffValue.toString())
}
import moment from "moment"

function getBeijingTime(originalTime: string, format: string = "YYYY-MM-DD HH:mm:ss") {
  const beijingTime = moment(originalTime).utcOffset("+08:00") // 将时间转换为北京时间
  // 如果传入了格式化字符串，则根据格式化字符串格式化时间
  if (format) {
    return beijingTime.format(format)
  }
  return beijingTime.format("YYYY-MM-DD HH:mm:ss") // 默认格式化为北京时间
}
function friendlyFileSize(bytes: number): string {
  const units = ["B", "KB", "MB", "GB", "TB"]

  let unitIndex = 0
  while (bytes >= 1024) {
    bytes /= 1024
    unitIndex++
  }

  return `${bytes.toFixed(2)}${units[unitIndex]}`
}
function getStringLength(str: string) {
  if (str == null) {
    return 0
  }

  if (typeof str !== "string") {
    return 0
  }

  return str.length
}
function shortenString(inputString: string, maxLength: number = 12): string {
  if (inputString.length <= maxLength) {
    return inputString
  }

  const ellipsis = ".."
  const halfEllipsisLength = Math.floor((maxLength - ellipsis.length) / 2)

  const beginning = inputString.slice(0, halfEllipsisLength)
  const end = inputString.slice(-halfEllipsisLength)

  return beginning + ellipsis + end
}
function shortenStringEnd(inputString: string, maxLength: number = 12) {
  if (inputString.length > maxLength) {
    return inputString.substring(0, maxLength - 3) + "..."
  } else {
    return inputString
  }
}
function replacePhoneWithAsterisk(phoneNumber: string) {
  if (phoneNumber.length !== 11) {
    return phoneNumber // 如果手机号码长度不为11位，则直接返回原始字符串
  }

  const middleStartIndex = 3
  const middleEndIndex = 7
  const replacedMiddle = phoneNumber.slice(0, middleStartIndex) + "****" + phoneNumber.slice(middleEndIndex)
  return replacedMiddle
}
function isEmpty(obj: object): boolean {
  if (obj === null) return true
  if (obj === undefined) return true
  if (Object.keys(obj).length < 1) return true
  return false
}
function getFileExtension(filename: string) {
  const parts = filename.split(".")
  return parts[parts.length - 1]
}
function getApiHostUrl() {
  return process.env.VUE_APP_GROUPX_HOST
}

import myStorage from "./my-storage"
function getAgent() {
  const pageFrom = myStorage.getPageFrom()
  let agent = "0xDb0a2D08A7217E7fb0eAD08FDF4d54d66e4365Ef"
  switch (pageFrom) {
    case "health_check": //体检报告
      agent = "0x742d35Cc6634C0532925a3b844Bc454e4438f44e" // 王晓丹体检报告
      break;
    case "weight_loss":
      agent = "0xDb0a2D08A7217E7fb0eAD08FDF4d54d66e4365Ef"
      break;
  }
  console.warn("=====>根据pageFrom指定agent:", pageFrom, agent)
  return agent
}
function getSsoClientId() {
  return process.env.VUE_APP_GROUPX_SSO_CLIENT_ID
}
function getUrlParam(name: string) {
  const hash = decodeURIComponent(location.hash)
  console.log("hash:", hash)

  const reg = new RegExp("[?|&]" + name + "=" + "([^&;]+?)(&|#|;|$)").exec(hash)

  return decodeURIComponent((reg || [, ""])[1].replace(/(\?.*|\+.*)/, "")) || null
}
function isCompleteURL(url: string) {
  const urlPattern = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$/i
  return urlPattern.test(url)
}

function generateMD5(input: string) {
  var md5 = require("spark-md5")
  var hash = md5.hash(input)
  console.log(hash)
}
export {
  getAgent,
  getApiHostUrl,
  getSsoClientId,
  isEmpty,
  getBeijingTime,
  friendlyDate,
  friendlyFileSize,
  getStringLength,
  getUrlParam,
  shortenString,
  shortenStringEnd,
  generateMD5,
  isCompleteURL,
  getFileExtension,
  replacePhoneWithAsterisk,
}
