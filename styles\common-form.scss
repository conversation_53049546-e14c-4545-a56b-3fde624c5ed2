// 公共表单样式
@import '@/styles/variables.scss';

// 容器样式
.form-container {
  width: 100%;
  height: 100vh;
  background-color: $primary-color;
  text-align: center;
  color: $text-primary;
  padding: 60rpx 40rpx;
  position: relative;
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

// 背景图片
.background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0.08;
  z-index: 1;
}

// 标题样式
.title {
  margin-top: 60rpx;
  font-size: 48rpx;
  font-weight: 600;
  display: block;
  padding: 20rpx;
  letter-spacing: 4rpx;
  z-index: 2;
}

// 步骤指示器
.step-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30rpx auto;
  width: 80%;
  z-index: 2;

  .step {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    color: $text-primary;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    font-weight: 500;
    position: relative;

    &.active {
      background-color: $text-primary;
      color: $primary-color;
      box-shadow: 0 0 10rpx rgba(255, 255, 255, 0.5);
    }

    &.completed {
      background-color: rgba(255, 255, 255, 0.8);
      color: $primary-color;
    }
  }

  .step-line {
    flex: 1;
    height: 2rpx;
    background-color: rgba(255, 255, 255, 0.3);
    margin: 0 10rpx;

    &.completed {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }
}

// 副标题样式
.sub-title {
  margin-top: 30rpx;
  margin-bottom: 15rpx;
  font-size: 32rpx;
  text-align: center;
  line-height: 40rpx;
  padding: 10rpx;
  color: rgba(255, 255, 255, 0.9);
  letter-spacing: 2rpx;
  z-index: 2;
}

// 内容区域
.content-section {
  flex: 1;
  margin-top: 15rpx;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 15rpx;
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

// 区域标题
.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: $text-primary;
  margin: 10rpx 0;
  text-align: left;
  z-index: 2;
  padding: 0 5rpx;
}

// 输入项
.input-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 48%;
  margin-bottom: 20rpx;

  &.full-width {
    width: 100%;
  }

  .input-label {
    font-size: 28rpx;
    color: $text-primary;
    margin-bottom: 10rpx;
    text-align: left;
    font-weight: 500;
  }

  .input-hint {
    font-size: 24rpx;
    color: rgba(255, 255, 0, 0.6);
    margin-top: 8rpx;
    text-align: left;
  }
}

// 提示文本
.tips-text {
  font-size: 24rpx;
  color: rgba(255, 255, 0, 0.6); // 黄色提示文字
  margin-bottom: 15rpx;
  margin-top: 5rpx;
  max-width: 90%;
  text-align: left;
}

// 底部区域
.footer {
  margin-top: auto;
  padding-bottom: 50rpx;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .tips-text {
    text-align: center;
    margin-bottom: 20rpx;
  }
}

// 按钮样式
// 全局按钮基础样式
%button-base {
  outline: none; // 移除轮廓
  border: none; // 移除边框
  background-clip: padding-box; // 防止边框溢出
  -webkit-appearance: none; // 移除 WebKit 默认样式
  -webkit-tap-highlight-color: transparent; // 移除点击高亮
  &:after {
    border: none; // 移除小程序按钮默认边框
  }
}

button {
  @extend %button-base;
}

.next-button {
  @extend %button-base;
  color: $primary-color;
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: $text-primary;
  border: none;
  border-radius: 44rpx;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
  font-size: 32rpx;
  font-weight: 500;
  z-index: 2;
}

.next-button-disabled {
  color: rgba(255, 255, 255, 0.5);
  width: 80%;
  height: 88rpx;
  line-height: 88rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  z-index: 2;
}

button[disabled] {
  opacity: 0.5;
  background-color: rgba(255, 255, 255, 0.3);
  color: rgba(255, 255, 255, 0.5);
}
