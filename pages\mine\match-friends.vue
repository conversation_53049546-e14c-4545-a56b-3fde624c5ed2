<template>
  <view class="content">
    <image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
    <view class="title-container">
      <view class="title">健康生活•科学减重</view>
    </view>
    <view class="match-info">{{ matchInfo }}</view>
    <button type="primary" class="btn-continue" disabled>
      运算...
    </button>
  </view>
</template>

<script>
import groupx from "@/api/api-groupx"
import myStorage from '../../common/my-storage';


export default {

  data() {
    return {
      bg_img: require('@/static/640.png'),
      matchInfo: "智能算法加速运行中...",
      userInfo: {},
      logging: true,
      myTimer: null
    }
  },
  onLoad() {
    this.myTimer = setInterval(() => {
      console.log("matchInfo:", this.matchInfo, this.matchInfo.length)
      if (this.logging) {
        this.matchInfo = this.matchInfo === '智能算法加速运行中...' ? '智能算法加速运行中.   ' : '智能算法加速运行中...';
      }
    }, 1000);
    this.userInfo = myStorage.getUserInfo()
    console.log("index onLoad userInfo:", this.userInfo)
    if (!this.userInfo || !this.userInfo.account) { this.showModalError("用户信息不完善,请重新登录。") }

    const { era, gender, account } = this.userInfo?.health
    const myConditions = { era, gender, account }
    const myLongFor = this.userInfo?.vivid?.longFor

    groupx.submitHealthSurvey({ 
      ...this.userInfo?.health,
       ...this.userInfo.healthData,
      userObjectId: this.userInfo.userObjectId
     }).then(res => {
      console.log("submitHealthSurvey:", res)
    }).catch(err => {
      this.showModalError("保存用户信息发生错误，请重新登录。")
    })
    groupx.updateUser(this.userInfo).then(res => {
      console.log("updateUser:", res)
      if (res.code === 200) {
        this.myTimer = null
        this.userInfo = res.data
        myStorage.setUserInfo(this.userInfo)
        setTimeout(() => {
          this.myTimer = null
          uni.reLaunch({ url: '/pages/result' })
        }, 4268)
      } else {
        this.showModalError("保存用户信息失败，请重新登录。")
      }
    }).catch(err => {
      this.showModalError("保存用户信息发生错误，请重新登录。")
    })
  },
  methods: {
    showModalError(content) {
      uni.showModal({
        showCancel: false,
        title: "匹配失败",
        content,
        complete: function (res) { uni.reLaunch({ url: '/pages/ads/index' }) }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-color: $primary-color;
  opacity: 0.9;
  position: relative;
}

.title-container {
  margin-top: 20vh;
  z-index: 2;
  display: flex;
  justify-content: center;
  width: 100%;
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: $text-primary;
  text-align: center;
  z-index: 2;
  animation: letterspacing 5s infinite alternate ease-in-out;
  letter-spacing: -2.2rem;
  white-space: nowrap;
  transform: translate3d(0, 0, 0);
}

.match-info {
  margin-top: 15vh;
  font-size: 36rpx;
  color: $text-secondary;
  letter-spacing: 2rpx;
  z-index: 2;
  text-align: center;
}

.btn-continue {
  width: 60%;
  color: $btn-text-color;
  height: 88rpx;
  line-height: 88rpx;
  margin-top: 20vh;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  border-radius: 44rpx;
  box-shadow: $btn-shadow;
  font-size: 32rpx;
  font-weight: 500;
  z-index: 2;
}

.background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: $bg-overlay;
  opacity: 0.08;
  z-index: 1;
}

@keyframes letterspacing {
  0% {
    letter-spacing: -2.2rem;
    filter: blur(.3rem);
  }

  50% {
    filter: blur(.5rem);
  }

  100% {
    letter-spacing: .5rem;
    filter: blur(0rem);
  }
}
</style>
