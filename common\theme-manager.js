/**
 * 主题管理模块
 * 用于管理应用的主题配色方案
 */

import myStorage from './my-storage';

// 主题类型枚举
export const THEME_TYPES = {
  DEFAULT: 'default',  // 默认主题 - 维多利亚蓝/草绿色
  LAKE_BAMBOO: 'lake_bamboo',  // 湖水蓝/竹篁绿主题
  COFFEE: 'coffee'  // 咖啡主题 - 咖啡棕/黑色/白色
};

class ThemeManager {
  constructor() {
    this.currentTheme = null;
    this.init();
  }

  /**
   * 初始化主题管理器
   */
  init() {
    // 从本地存储获取当前主题
    const savedTheme = uni.getStorageSync('app_theme') || THEME_TYPES.DEFAULT;
    this.currentTheme = savedTheme;

    // 应用主题
    this.applyTheme(savedTheme);

    return savedTheme;
  }

  /**
   * 获取当前主题
   * @returns {string} 当前主题类型
   */
  getCurrentTheme() {
    // #ifdef MP
    // 优先从storage中获取主题
    const theme = uni.getStorageSync('current_theme');
    if (theme) {
      return theme;
    }
    // #endif
    
    return this.currentTheme;
  }

  /**
   * 切换主题
   * @param {string} themeType 主题类型
   * @returns {string} 切换后的主题类型
   */
  switchTheme(themeType) {
    if (!Object.values(THEME_TYPES).includes(themeType)) {
      console.error('无效的主题类型:', themeType);
      return this.currentTheme;
    }

    // 保存主题设置
    uni.setStorageSync('app_theme', themeType);
    this.currentTheme = themeType;

    // 应用主题
    this.applyTheme(themeType);

    return themeType;
  }

  /**
   * 应用主题
   * @param {string} themeType 主题类型
   */
  applyTheme(themeType) {
    // 触发主题变更事件
    uni.$emit('theme-changed', themeType);

    // 设置主题
    try {
      // #ifdef H5
      document.documentElement.setAttribute('data-theme', themeType);
      // #endif

      // #ifdef MP
      // 小程序环境下，使用uni.setStorageSync存储主题状态
      uni.setStorageSync('current_theme', themeType);
      
      // 如果getApp()可用，则同时更新globalData
      const app = getApp();
      if (app) {
        app.globalData = app.globalData || {};
        app.globalData.currentTheme = themeType;
      }
      // #endif
    } catch (e) {
      console.error('设置主题失败:', e);
    }
  }

  /**
   * 获取主题名称
   * @param {string} themeType 主题类型
   * @returns {string} 主题名称
   */
  getThemeName(themeType) {
    const themeNames = {
      [THEME_TYPES.DEFAULT]: '默认主题',
      [THEME_TYPES.LAKE_BAMBOO]: '湖竹主题',
      [THEME_TYPES.COFFEE]: '咖啡主题'
    };

    return themeNames[themeType] || '未知主题';
  }
}

export default new ThemeManager();
