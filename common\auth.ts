import myStorage from "./my-storage"
import groupx from "../api/api-groupx"
import { getAgent, getSsoClientId } from './util'
import { IUserInfo } from './type'

interface PhoneInfo {
  phoneNumber: string
  purePhoneNumber: string
  countryCode: string
}

interface PlatformUser {
  id: string
  account: string
  platformType: number
  [key: string]: any
}

interface UserInfo {
  account: string
  addresses: string[]
  avatar: string
  balanceAITokens: number
  balanceCNY: number
  balanceScore: number
  city: string
  cotaAddresses: string[]
  country: string
  district: string
  era: number
  gender: string
  id: number
  name: string
  nickName: string
  objectId: string
  permissions: string[]
  phoneInfo: PhoneInfo
  platformUsers: PlatformUser[]
  publicKey: string | null
  region: string
  roles: string[]
  ssoID: string
  status: string
  totalAITokens: number
  usedAITokens: number
  userObjectId: string
  userName?: string
  avatarUrl?: string
  [key: string]: any
}

interface LoginResponse {
  success: boolean
  ssoID: string
  sessionId: string
  expiresAt: string
}

/**
 * 自动登录并获取用户信息
 * @returns {Promise<IUserInfo>} 返回登录结果
 */
export const autoLoginAndGetUserInfo = (): Promise<IUserInfo> => {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: "weixin",
      success: function (res: any) {
        console.log("uni.login获取weixin登录code：", res)
        uni.getUserInfo({
          provider: 'weixin',
          success: function (info: any) {
            console.log("uni.getUserInfo获取weixin用户信息：", info)
            let userInfo: IUserInfo | null = null
            if (info.userInfo) {
              // 将微信用户信息转换为 IUserInfo 格式
              const wxUserInfo: IUserInfo = {
                userId: info.userInfo.openid || '',
                userObjectId: info.userInfo.openid || '',
                sub: info.userInfo.openid || '',
                openid: info.userInfo.openid || '',
                account: info.userInfo.openid || '',
                name: info.userInfo.nickName || '',
                avatarUrl: info.userInfo.avatarUrl || '',
                phoneInfo: info.userInfo.phoneInfo || {},
                permissions: [],
                roles: [],
                balanceAITokens: 0,
                usedAITokens: 0,
                totalAITokens: 0
              }
              userInfo = myStorage.setUserInfo(wxUserInfo)
              console.log("保存weixin用户信息：", userInfo)
            }
            if (res.code) {
              const code = res.code
              console.log("获取weixin登录code：", code)
              groupxLogin(code).then((res:any) => {
                console.log("groupxLogin成功：", res)
                resolve(res)
              }).catch(err => {
                reject(err)
              })
            } else {
              resolve(userInfo as IUserInfo)
            }
          },
          fail: () => {
            reject()
          }
        })
      },
      fail: () => {
        reject()
      }
    })
  })
}

/**
 * 调用后端登录接口
 * @param {string} code 微信登录code
 * @returns {Promise<IUserInfo>} 返回登录结果
 */
export const groupxLogin = (code: string): Promise<IUserInfo> => {
  return new Promise((resolve, reject) => {
    const ssoClientId = getSsoClientId()
    groupx.wxMiniProgramLogin({
      platformName: 'GATESSO',
      agent: getAgent(),
      authData: {
        code,
        ssoClientId,
        scope: "read",
        tag: "wechat_miniprogram"
      }
    })?.then((res: any) => {
      console.info("微信小程序登录返回：", res)
      if (res.success) {
        groupx.getUserInfo(res.ssoID)?.then((userInfo: any) => {
          console.log("获取用户信息5：", userInfo)
          // 将后端返回的用户信息转换为 IUserInfo 格式
          const formattedUserInfo=userInfo
          myStorage.setUserInfo(formattedUserInfo)

          myStorage.setSession({
            sessionId: res.sessionId,
            expiresAt: res.expiresAt,
          })

          groupx.onLogin()
          resolve(formattedUserInfo)
        }).catch(err => {
          reject(err)
        })
      } else {
        reject(new Error('登录失败'))
      }
    }).catch(function (error: any) {
      console.log("微信登录失败", error)
      uni.showToast({
        title: error.code == "ERR_NETWORK" ? "网络错误" : "微信登录失败",
        icon: "none",
        duration: 5000,
      })
      reject(error)
    })
  })
} 