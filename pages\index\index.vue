<template>
  <view class="content" :animation="animationData">
    <image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>

    <view class="logo">
      <logo logoSize="100rpx" subTitleSize="34rpx" />
    </view>

    <view class="slogan">
      <text class="main-text"></text>
      <text class="sub-text">定制专属计划，陪伴你的健康之旅</text>
    </view>

    <input type="nickname" :placeholder="nickName || '点击选择微信昵称'" :value="nickName"
      placeholder-style="text-align: center; color: #888;" style="text-align: center;" @change="onNickNameChange"
      @input="onNickNameInput" class="btn-login">
    </input>


    <!-- 隐私提示弹窗 -->
    <uni-popup ref="privacyPopup" type="center">
      <view class="privacy-popup">
        <view class="privacy-title">隐私提示</view>
        <view class="privacy-content">
          <text>为了给您提供更专业的健康管理服务，我们需要获取您的手机号，用于：</text>
          <view class="privacy-list">
            <text>1. 为您匹配减重医生，制定个性化方案</text>
            <text>2. 帮助医生了解您的健康档案，精准建议</text>
            <text>3. 建立有效沟通桥梁，确保服务及时有效</text>
          </view>
          <text>您的信息将被严格保密，您可以随时在"设置"中取消授权。</text>
        </view>
        <view class="privacy-buttons">
          <button class="privacy-btn cancel" @click="handlePrivacyCancel" style="border: none;">再看看</button>
          <button class="privacy-btn confirm" @click="handlePrivacyConfirm" style="border: none;">好的</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import userInfoBtn from '@/components/common/userInfoBtn.vue';
import formIdMixin from '@/utils/form-id-mixins.js';

import groupx from "@/api/api-groupx";
import myStorage from '../../common/my-storage';
import logo from '@/components/common/logo'

export default {
  components: {
    userInfoBtn, logo
  },
  mixins: [formIdMixin],

  computed: {
    phoneMask() {
      const phoneInfo = this.userInfo.phoneInfo
      if (phoneInfo) {
        const phone = phoneInfo.phoneNumber
        if (phone) {
          return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2")
        }
      }
      return ""
    }
  },
  data() {
    return {
      bg_img: require('@/static/640.png'),
      title: '',
      indicatorDots: true,
      userInfo: myStorage.getUserInfo(),
      pageFrom: myStorage.getPageFrom(),
      logging: false,
      isFromAds: false, // 添加标记，用于判断是否从广告页跳转而来
      showPrivacyPopup: false,
      pendingPhoneAuth: null,
      nickName: ''
    }
  },
  onLoad(options) {
    console.log("index onLoad---------", options)
    this.userInfo = myStorage.getUserInfo()
    this.pageFrom = myStorage.getPageFrom()

    setTimeout(() => {

      // this.$refs.privacyPopup.open()

      console.log("------------>:", myStorage.getPageFrom())
    }, 2500)

    console.log("index onLoad userInfo:", this.userInfo)
    console.log("index onLoad userInfo:", this.pageFrom)
  },
  onReady() {
    this.nickName = this.userInfo?.nickName
  },
  methods: {
    isLogin() {
      console.log("isLogin:", this.userInfo, this)
      return (this.userInfo && this.userInfo.id && this.userInfo.phoneInfo)
    },
    isHavePhone() {
      return true
      console.log("isHavePhone:", this.userInfo, this)
      return (this.userInfo && this.userInfo.phoneInfo?.phoneNumber)
    },
    goNext() {
      let url = '/pages/health_weight_loss/health_weight_loss'

      if (this.pageFrom === 'health_check') {
        url = '/pages/health_check/index'
      } else if (this.userInfo.healthData) {
        url = '/pages/result'
      } else if (this.pageFrom === 'weightRecord') {
        url = '/pages/health_weight_loss/weight_record'
      }
      uni.navigateTo({ url })
    },

    getPhoneNumber(e) {
      const that = this
      console.log("获取手机号事件：", e)

      // 检查是否获取到code
      if (!e.detail.code) {
        uni.showToast({
          title: '获取手机号失败(未授权)',
          icon: 'none'
        })
        that.logging = false
        that.goNext()
        return
      }

      // 直接处理手机号授权
      that.logging = true
      console.log("获取手机号code：", e.detail.code)

      // 调用后端接口获取手机号
      groupx.getWxPhoneNumber(e.detail.code).then(res => {
        console.log("获取手机号结果：", res)
        if (res.code === 200) {
          // 获取成功，保存用户信息
          let phoneInfo = res.data
          if (phoneInfo) {
            console.log("获取手机号成功，保存用户信息：", user)
            const user = {
              ...that.userInfo,
              phoneInfo: phoneInfo
            }
            that.userInfo = myStorage.setUserInfo(user)
          }

          // 登录成功后的处理
          groupx.onLogin()

          // 跳转到下一步
          setTimeout(() => {
            that.logging = false
            that.goNext()
          }, 1000)
        } else {
          uni.showToast({
            title: res.errmsg || '获取手机号失败',
            icon: 'none'
          })
          that.logging = false
        }
      }).catch(err => {
        console.error('获取手机号失败：', err)
        uni.showToast({
          title: '获取手机号失败，请重试',
          icon: 'none'
        })
        that.logging = false
      })
    },
    onNickNameChange(e) {
      console.log('onNickNameChange', e);
      const newNickName = e.detail.value;
      console.log('新的昵称:', newNickName);

      if (newNickName) {
        this.userInfo = myStorage.getUserInfo()
        console.log('newNickName:', newNickName, this.userInfo);
        const user = {
          ...this.userInfo,
          nickName: newNickName,
        };
        this.userInfo = myStorage.setUserInfo(user);
        groupx.updateUserInfo(user)
          .then((newUser) => {
            if (newUser.code == 200) {
              myStorage.setUserInfo(newUser.data)

              if (newUser.data?.userObjectId) myStorage.setUserObjectId(newUser.data?.userObjectId)
              
              uni.showToast({
                title: '昵称更新成功', icon: 'success',
                complete: () => { this.goNext() }
              });
            }

          })
          .catch(() => {
            uni.showToast({ title: '昵称同步失败', icon: 'none', complete: () => { this.goNext() } });
          });
      } else {
        uni.showToast({ title: '昵称不能为空', icon: 'none' });
      }
    },
    onNickNameInput(e) {
      console.log("------------------->", e)
      uni.showToast({
        title: '请从底部菜单选择微信昵称,不要手工修改.', icon: 'none',
        duration: 3000,
      });
    },
    handlePrivacyCancel() {
      this.$refs.privacyPopup.close()
      this.logging = false
      // 用户拒绝授权，直接进入下一步
      this.goNext()
    },

    handlePrivacyConfirm() {
      this.$refs.privacyPopup.close()
      // 用户同意授权，显示获取手机号按钮
      this.logging = false
      // 显示提示
      // uni.showToast({
      //  title: '请点击下方按钮授权手机号',
      // icon: 'none',
      //duration: 2000
      //})
    },

    /**
* 轮询查询是否有sessionKey,防止初始化接口响应时间过长拿不到解密入参，用户解密失败
*/
    handlePolling(e) {
      this.timer = setInterval(() => {
        if (this.sessionKey) {
          this.decryptDataInfo(e.detail)
          this.timer = null
        }
      }, 300)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-color: $primary-color;
  opacity: 0.9;
  position: relative;
  transition: opacity 0.4s ease-out;
  /* 调整过渡效果 */
  animation: fadeIn 0.4s ease-out;
  /* 调整淡入动画 */
  will-change: opacity, transform;
  /* 优化性能 */
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(1.02);
    /* 添加轻微缩放效果 */
  }

  to {
    opacity: 0.9;
    transform: scale(1);
  }
}

.logo {
  margin-top: 15vh;
  z-index: 2;
}

.slogan {
  margin-top: 15vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  opacity: 0.3;

  .main-text {
    font-size: 48rpx;
    color: $text-primary;
    font-weight: 600;
    margin-bottom: 60rpx;
    letter-spacing: 4rpx;
  }

  .sub-text {
    font-size: 32rpx;
    color: $text-secondary;
    letter-spacing: 2rpx;
  }
}

.btn-login {
  width: 60%;
  color: $btn-text-color;
  height: 88rpx;
  line-height: 88rpx;
  margin-top: 20vh;
  background-color: $btn-bg-color;
  border: none;
  border-radius: 44rpx;
  box-shadow: $btn-shadow;
  font-size: 32rpx;
  font-weight: 500;
  z-index: 2;
}

.background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: $bg-overlay;
  opacity: 0.08;
  z-index: 1;
}

.privacy-popup {
  width: 600rpx;
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 40rpx;
  box-sizing: border-box;
}

.privacy-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
  margin-bottom: 30rpx;
}

.privacy-content {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 40rpx;

  .privacy-list {
    margin: 20rpx 0;
    padding-left: 20rpx;

    text {
      display: block;
      margin-bottom: 10rpx;
    }
  }
}

.privacy-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.privacy-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  outline: none;
  background-clip: padding-box;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;

  &:after {
    border: none;
  }

  &.cancel {
    background-color: #F5F5F5;
    color: #666666;
  }

  &.confirm {
    background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
    color: #FFFFFF;
  }
}
</style>
