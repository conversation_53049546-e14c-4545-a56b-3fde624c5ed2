import { checkResponse } from "../common/check-response";
import {
  IMyKnowledge,
  IUserInfo,
  ISessionInfo,
  IUserInfoGroupX,
  EthAddress,
  ITotalInfoUser,
  IBodyCondition,
  IWeightManagement,
  IHealthSurvey,
  IHealthReport,
  WxUserInfo,
  WxGroupInfo
} from "../common/type";
import { makeReqParams } from "../common/make-request";
import * as utils from "../common/util";
import { ApiBase } from "./api-base";
// import base64s from "../utils/ali/base64s";
const base64s = require("../utils/ali/base64s");

type METHOD_TYPE =
  | "GET"
  | "HEAD"
  | "POST"
  | "PUT"
  | "DELETE"
  | "TRACE"
  | "CONNECT";
type IWXGroupInfo = {
  account: EthAddress;
  groupName: string;
  groupId: string;
  avatar: string;
  joinWelcomeInfo?: string;
  description?: string;
};
class APIGroupX extends ApiBase {
  async wxLogin(code: string) {
    const that = this;
    return this.wxMiniProgramLogin({
      platformName: "GATESSO",
      agent: utils.getAgent(),
      authData: {
        code,
        scope: "read",
        tag: "wechat_miniprogram",
      },
    })
      ?.then((res: any) => {
        console.info("微信小程序 wx.login 登录返回：", res);
        if (res.success) {
          return that.getUserInfo(res.ssoID)?.then((userInfo: any) => {
            console.log("group userInfo:", userInfo);
            that.user = userInfo;
            that.session = res;
            return { userInfo, res };
          });
        }
      })
      .catch(function (error) {
        console.log("微信登录失败");
        console.log(error);
        return { success: false };
      });
  }

  // ------------------------------------------------------------
  // my knowledge
  // 将reply的记录加到 my knowledge中,同时修改reply 中的favrited标志
  addMyKnowledge(data: IMyKnowledge) {
    console.log("addMyKnowledge:", data);
    return new Promise((reslove, reject) => {
      uni.request({
        method: "POST",
        url:
          utils.getApiHostUrl() + "/v1/chat/my-knowledge/" + this.user.account,
        data: {
          ...this.session,
          ...makeReqParams(data),
          payload: data,
        },
        success: (result) => {
          const recvData = checkResponse(result);
          console.log("add my knowledge:", recvData);
          if (recvData === null) reject({ errors: "添加知识库失败." });
          if (
            recvData &&
            typeof recvData.code === "number" &&
            recvData.code !== 0
          )
            reject({ errors: recvData?.errors || "添加知识库失败" });
          reslove(recvData);
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  updateMyKnowledge(data: IMyKnowledge) {
    console.log("updateMyKnowledge:", data);
    return new Promise((reslove, reject) => {
      uni.request({
        method: "PUT",
        url:
          utils.getApiHostUrl() + "/v1/chat/my-knowledge/" + this.user.account,
        data: {
          ...this.session,
          ...makeReqParams(data),
          payload: data,
        },
        success: (result) => {
          const recvData = checkResponse(result);
          console.log("UPDATE my knowledge:", recvData);
          if (recvData === null) reject({ errors: "添加知识库失败." });
          if (typeof recvData.code === "number" && recvData.code !== 0)
            reject({ errors: recvData?.errors || "添加知识库失败" });
          reslove(recvData);
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  getMyKnowledges(data: any) {
    return new Promise((reslove, reject) => {
      uni.request({
        method: "POST",
        url:
          utils.getApiHostUrl() +
          "/v1/chat/my-knowledge/list/" +
          this.user.account +
          "/" +
          this.session.sessionId,
        data,
        success: (res) => {
          if (res.statusCode === 200) {
            reslove(checkResponse(res));
          } else {
            reject(res);
          }
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  getMyKnowledgeDetail(objectId: string) {
    return new Promise((reslove, reject) => {
      uni.request({
        method: "GET",
        url:
          utils.getApiHostUrl() +
          "/v1/chat/my-knowledge/" +
          this.session.sessionId +
          "/" +
          objectId,
        success: (res) => {
          if (res.statusCode === 200) {
            reslove(checkResponse(res));
          } else {
            reject(res);
          }
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  delMyKnowledge(objectId: string) {
    return new Promise((reslove, reject) => {
      uni.request({
        method: "DELETE",
        url:
          utils.getApiHostUrl() +
          `/v1/chat/my-knowledge/${this.session.sessionId}/${objectId}`,
        data: {
          ...this.session,
          ...makeReqParams({}),
          payload: {},
        },
        success: (res) => {
          if (res.statusCode === 200) {
            reslove(checkResponse(res));
          } else {
            reject(res);
          }
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  //------------------------------------------------------------
  //减重相关请求
  //获取最近N天的汇总数据
  fetchWeightLosssLastDay(reqJson: {
    userObjectId: string, lastDays: number, chart_type: "周报" | "月报" | "其他",
    itchatUser: WxUserInfo, itchatGroup: WxGroupInfo
  }) {
    return this.gRequest(
      `/v1/health/weight-loss/last-data/${this.user.account}`,
      reqJson
    );
  }

  // ------------------------------------------------------------
  // chat list
  getChatHistoryByCID(cid: string, pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list/cid/${this.session.sessionId}/${pageSize}/${pageNum}/${cid}/sign`
    );
  }
  getChatHistory(otherSide: string, pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list/other-side/${this.session.sessionId}/${pageSize}/${pageNum}/${otherSide}/sign`
    );
  }
  getChatListMySent(pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list/my-sent/${this.session.sessionId}/${pageSize}/${pageNum}/sign`
    );
  }
  getChatListToMe(pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list/to-me/${this.session.sessionId}/${pageSize}/${pageNum}/sign`
    );
  }
  getChatListAll(pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list-all/${this.session.sessionId}/${pageSize}/${pageNum}/sign`
    );
  }
  getChatListNoReply(pageSize: number, pageNum: number) {
    return this.gRequest(
      `/v1/chat/list-all/no-reply/${this.session.sessionId}/${pageSize}/${pageNum}/sign`
    );
  }
  getChatSummary(data: {
    receiver: EthAddress;
    receiverName: string;
    wxGroupName: string;
    wxGroupId: string;
  }) {
    return this.gRequest(
      `/v1/chat/summary/${this.session.sessionId}/sign`,
      data
    );
  }
  getChatDetail(objectId: string) {
    const uri = `/v1/chat/detail/${this.session.sessionId}/${objectId}/sign`;
    return this.gRequest(uri);
  }
  delChat(objectId: string) {
    const uri = `/v1/chat/${this.session.sessionId}/${objectId}`;
    return this.gRequest(uri, undefined, "DELETE");
  }
  getMyTotalInfo() {
    const uri = `/v1/chat/total-info/${this.session.sessionId}/sign`;
    return this.gRequest(uri);
  }
  postChatReply(data: any) {
    console.log("postChatReply:", data);
    return new Promise((reslove, reject) => {
      uni.request({
        method: "PUT",
        url: utils.getApiHostUrl() + "/v1/chat/" + this.user.account,
        data: {
          ...this.session,
          ...makeReqParams(data),
          payload: data,
        },
        success: (result) => {
          const recvData = checkResponse(result);
          console.log("add my knowledge:", recvData);
          if (recvData === null) reject({ errors: "回复信息失败." });
          if (typeof recvData.code === "number" && recvData.code !== 0)
            reject({ errors: recvData?.errors || "回复信息失败" });
          reslove(recvData);
        },
        fail: (err) => {
          reject(err);
        },
        complete: () => { },
      });
    });
  }
  wxMiniProgramLogin(data: { agent: string, authData: any, platformName: string }) {
    return this.gRequest("/v1/user/connect", data);
  }

  getWxPhoneNumber(code: string) {
    console.log("getWxPhoneNumber", this.session, this.user);
    return this.gRequest("/v1/wechat/get-user-phonenumber", {
      ...this.session,
      code,
    });
  }
  getWxUserProfile(res: any) {
    return this.gRequest("/v1/wechat/get-user-profile", {
      ...this.session,
      res,
    });
  }

  postWxNotice(data: any) {
    return this.gRequest("/v1/wechat/notice/" + data.toUser, data);
  }
  postChat(data: any) {
    const uri = `/v1/chat/${this.user.account}`;
    return this.gRequest(uri, data);
  }
  // ------------------------------------------------------------
  // my reply
  getMyReplyList(data: any) {
    console.log("getMyReplyList:", this.session, data);
    const uri = "/v1/chat/my-reply/list/" + this.session.sessionId;
    return this.gRequest(uri, data);
  }
  getMyReplyDetail(objectId: string) {
    const uri = `/v1/chat/my-reply/${this.session.sessionId}/${objectId}`;
    return this.gRequest(uri);
  }
  getReplyListByChatId(
    chatId: string,
    data: {
      pageSize: number;
      pageNum: number;
    }
  ) {
    // console.log("getReplyListByChatId:", this.session, chatId)
    const uri = "/v1/chat/reply/list/chatid/" + chatId;

    return this.gRequest(uri, { ...data, sessionId: this.session.sessionId });
  }
  // addMyFavrited(objectId: string) {
  //   const uri = `/v1/chat/my-reply/${this.session.sessionId}/${objectId}`
  //   return this.gRequest(uri)
  // }
  // ------------------------------------------------------------
  // user info
  updateUserInfo(data: IUserInfoGroupX) {
    return this.gRequest(`/v1/user/${this.user.account}`, data, "PUT");
  }
  getUserInfo(sso: string) {
    return this.gRequest("/v1/user/sso/" + sso);
  }
  getUserInfoByToken(code: string) {
    return this.gRequest("/v1/platform/user/access_token", {
      platformName: "GATESSO",
      code,
    });
  }

  getUserInfoDetail(account: string) {
    // console.log("getUserInfoDetail:", account)
    return this.gRequest("/v1/user/details/" + account, {}, "POST");
  }
  getUserInfoByAccount(account: string) {
    return this.gRequest("/v1/user/" + account);
  }
  getDoctorList() {
    return this.gRequest(`/v1/user/doctor/list/${this.session.sessionId}/sign`);
  }
  postLogout() {
    const ret = this.gRequest(
      "/v1/wechat/logout/",
      { sessionId: this.session.sessionId },
      "POST"
    );

    this.onLogout();
    return ret;
  }
  postFeedback(body: { question: string; contact: string }) {
    return this.gRequest(
      "/v1/util/feedback/" + this.session.sessionId,
      body,
      "POST"
    );
  }
  // ------------------------------------------------------------
  // user docment
  postUserDoc(data: { title: string; html: string }) {
    return this.gRequest("/v1/doc", data, "POST");
  }
  getDocListByAccount(account: string) {
    return this.gRequest(`/v1/doc/list/${account}/${this.session.sessionId}`);
  }
  getDocDetail(objectId: string) {
    return this.gRequest(`/v1/doc/${objectId}/${this.session.sessionId}`);
  }
  updateUserDoc(data: { title: string; html: string }) {
    return this.gRequest("/v1/doc", data, "PUT");
  }
  deleteUserDoc(objectId: string) {
    return this.gRequest("/v1/doc/" + objectId, undefined, "DELETE");
  }
  getMyFriends(account: string) {
    return this.gRequest(`/v1/user/friends/${this.session.sessionId}/:sign`);
  }
  //----------------------------------------

  getWxGroups(account: string) {
    return this.gRequest(`/v1/user/wxgroups/${this.session.sessionId}/:sign`);
  }
  getWxGroup(groupObjectId: string) {
    return this.gRequest(
      `/v1/user/group/${groupObjectId}/${this.session.sessionId}/:sign`
    );
  }
  updateWxGroup(groupObjectId: string, data: IWXGroupInfo) {
    return this.gRequest(`/v1/user/wxgroup/update/${groupObjectId}`, data);
  }
  //----------------------------------------
  getMyFriendDetail(account: string, friendId: string) {
    return this.gRequest(
      `/v1/user/friends/${account}/${this.session.sessionId}/${friendId}`
    );
  }
  getMyFriendTimers({
    account,
    name,
    recvObjectId,
    recvIsGroup,
  }: {
    account: string;
    name: string;
    recvObjectId: string;
    recvIsGroup: boolean;
    friendAccount: string;
  }) {
    return this.gRequest(`/v1/user/friend/get-timers/${this.user.account}`, {
      account,
      name,
      recvObjectId,
      recvIsGroup,
    });
  }
  addMyFriendTimer(timer: {
    account: string;
    name: string;
    perios: string;
    userId: string;
    event: string;
    msg: string;
    url: string;
    isGroup: boolean;
    recvObjectId: string;
  }) {
    return this.gRequest(
      `/v1/user/friend/timer/${this.user.account}`,
      timer,
      "POST"
    );
  }
  delMyFriendTimer(timer: {
    account: string;
    name: string;
    perios: string;
    userId: string;
    event: string;
    msg: string;
  }) {
    return this.gRequest(
      `/v1/user/friend/timer/${this.user.account}`,
      timer,
      "DELETE"
    );
  }
  //--------------------------------------
  // my group
  getMyGroupTimers({
    account,
    name,
    recvObjectId,
    recvIsGroup,
  }: {
    account: string;
    name: string;
    recvObjectId: string;
    recvIsGroup: boolean;
  }) {
    return this.gRequest(`/v1/user/group/get-timers/${this.user.account}`, {
      account,
      name,
      recvIsGroup,
      recvObjectId,
    });
  }
  addMyGroupTimer(timer: {
    account: string;
    name: string;
    perios: string;
    userId: string;
    event: string;
    msg: string;
    url: string;
  }) {
    return this.gRequest(
      `/v1/user/group/timer/${this.user.account}`,
      timer,
      "POST"
    );
  }
  delMyGroupTimer(timer: {
    account: string;
    name: string;
    perios: string;
    userId: string;
    event: string;
    msg: string;
  }) {
    return this.gRequest(
      `/v1/user/group/timer/${this.user.account}`,
      timer,
      "DELETE"
    );
  }
  //--------------------------------------
  // 随访信息
  getFollowUpList(data: { pageSize: number; pageNum: number }) {
    return this.gRequest(
      `/v1/user/follow-up/list/${this.user.account}`,
      data,
      "POST"
    );
  }
  addFollowUpInfo(data: { content: string; tag: string }) {
    return this.gRequest(`/v1/user/follow-up/${this.user.account}`, data);
  }
  delFollowUpInfo(data: { objectId: string }) {
    return this.gRequest(
      `/v1/user/follow-up/${this.user.account}`,
      data,
      "DELETE"
    );
  }
  //--------------------------------------
  // VIVID
  addTag(data: { tag: string; type: string }) {
    return this.gRequest(`/v1/vivid/add-tag`, data);
  }
  getTags(type: string = "all") {
    return this.gRequest(`/v1/vivid/get-tags/${this.user.account}/${type}`);
  }
  delTag(data: { type: string; objectId: string }) {
    return this.gRequest(`/v1/vivid/del-tag/${this.user.account}`, data);
  }
  matchFriends(data: any) {
    return this.gRequest(`/v1/vivid/match-friends/${this.user.account}`, data);
  }
  updateUser(data: any) {
    return this.gRequest(`/v1/user/${this.user.account}`, data, "PUT");
  }
  submitHealthSurvey(data: IHealthSurvey) {
    return this.gRequest(`/v1/health/survey/${this.user.account}`, data);
  }
  getHealthSurvey() {
    return this.gRequest(`/v1/health/survey/${this.user.account}`) as Promise<{ data: IHealthSurvey }>;
  }
  getHealthReport(userObjectId: string) {
    return this.gRequest(`/v1/health/health-report/嘉定区华亭镇社区卫生服务中体检报告/${userObjectId || this.user.userObjectId}`) as Promise<{ data: IHealthReport }>;
  }
  getHealthReportList(data: { pageSize: number; pageNum: number }) {
    return this.gRequest(`/v1/health/health-report/list/${this.user.userObjectId}/${data.pageNum}/${data.pageSize}`) as Promise<{ data: IHealthReport[] }>;
  }
  // sso 或account都可以,服务端会自动识别
  getVividUser(userId: string) {
    return this.gRequest(`/v1/vivid/get-user/${userId}`);
  }
  //--------------------------------------
  // VIVID message
  getVividMessageList(data: any) {
    return this.gRequest(`/v1/vivid/message/list`, data);
  }
  sendVividMessage(data: any) {
    return this.gRequest(`/v1/vivid/message/send`, {
      ...data,
      sessionId: this.session.sessionId,
    });
  }
  //--------------------------------------
  // aliyun oss
  // 获取上传路径、上传凭证L
  getAliSTSInfo() {
    return this.gRequest(`/v1/util/ali-oss/${this.session.sessionId}`);
  }
  getAliOssUrl(fileId: string) {
    const base64 = base64s.encode(fileId);
    return this.gRequest(
      `/v1/util/ali-oss/gen-url/bucket/${base64}/${this.session.sessionId}`
    );
  }
}
export default new APIGroupX();
