<template>
    <view class="datePopup-box">
        <uni-popup id="refPopup" ref="refPopup">
            <view class="popup-content">
                <view class="popup-title"> {{ pickerTitle }} </view>
                <picker-view mask-style="background:#1A1F27; z-index:0;height: 100%;"
                    indicator-class="picker-indicator-class" style="width: 90%; height: 200px;" class="timePicker"
                    indicator-style="height: 50px;background:#1A1F27; z-index:0;padding-right: 2px;" :value="region"
                    @change="bindChange" @pickstart="onPickstart" @pickend="onPickend">

                    <picker-view-column :class='citylist[0].children ? "item item1" : "item item1-s"'>
                        <view class="picker-item" v-for="(item, index) in citylist" :key="index" :id="item.label"
                            value="item.label">
                            {{ item.label }}
                        </view>
                    </picker-view-column>
                    <picker-view-column class="item item2" v-if="citylist[region[0]].children" :id="item.label">
                        <view class="picker-item" v-for="(item, index) in citylist[region[0]].children" :key="index">
                            {{ item.label }}
                        </view>
                    </picker-view-column>
                    <picker-view-column class="item item3" v-if="citylist[region[0]].children" :id="item.label">
                        <view class="picker-item"
                            v-for="(item, index) in citylist[region[0]].children[region[1]].children" :key="index">
                            {{ item.label }}
                        </view>
                    </picker-view-column>
                </picker-view>
            </view>
            <view class="btn-Block">
                <button class="left-btn " @click="onCancel">取消</button>
                <button class="right-btn " :class="changePopup == false ? 'right-bint-disable' : ''" @click="onSubmit">
                    保存</button>

            </view>
        </uni-popup>
    </view>
</template>

<script>
import myStorage from '../../common/my-storage'
import { fetchAddressDataFromTencent } from '../../common/address-utils'
export default {
    onReady() {
        console.log("onReady")
    },
    props: {

    },
    data() {
        return {
            pickerTitle: '选择城市',
            region: [0, 0, 0,],
            citylist: [],
            citylist2: [{
                label: '浙江', children: [{ label: '杭州', children: [{ label: '上城' }] }]
            }, {
                label: '四川', children: [{ label: '成都', children: [{ label: '锦里' }] }]
            }, {
                label: '江苏', children: [{ label: '南京', children: [{ label: '夫子庙' }] },
                { label: '苏州', children: [{ label: '相城' }] },
                { label: '无锡', children: [{ label: '惠山' }] },
                { label: '常州', children: [{ label: '钟楼' }, { label: '天宁' }, { label: '青羊' }] },
                { label: '扬州', children: [{ label: '广陵' }] },
                { label: '泰州', children: [{ label: '海陵' }] },
                { label: '宿迁', children: [{ label: '宿城' }] },
                ]
            }, {
                label: '广东', children: [{ label: '广州', children: [{ label: '黄浦江' }] }]
            }, {
                label: '山东', children: [{ label: '青岛', children: [{ label: '崂山' }] }]
            }, {
                label: '河南', children: [{ label: '郑州', children: [{ label: '金水' }] }]
            }, {
                label: '湖北', children: [{ label: '武汉', children: [{ label: '洪山' }] }]
            }, {
                label: '河北', children: [{ label: '石家庄', children: [{ label: '新华' }] }]
            }, {
                label: '山西', children: [{ label: '太原', children: [{ label: '迎泽' }] }]
            }],
            changePopup: true,
            selRegion: '',
            selCity: '',
            selDistrict: '',
            citytext: '',
            selGender: '',
            selEraText: '',
            selEra: [],
            lastPopup: '',
        }
    },
    methods: {
        fetchAddressData() {
            const that = this;
            this.citylist2 = myStorage.getRegionData() || this.citylist_x
            fetchAddressDataFromTencent().then(result => {
                that.citylist2 = result
            })
        },
        //弹出地址选择器
        showPopup(e) {
            this.lastPopup = e
            console.log('弹出地址选择器', e)
            if (e === 'era') {
                this.citylist = [{ label: '70后', value: 70 }, { label: '80后', value: 80 }, { label: '90后', value: 90 }, { label: '00后', value: 0 }]
                this.pickerTitle = '选择年龄段'
            } else if (e === 'gender') {
                this.citylist = [{ label: '女', value: 'woman' }, { label: '男', value: 'man' }]
                this.pickerTitle = '选择性别'
            } else {
                this.citylist = this.citylist2
                this.pickerTitle = '选择城市'
                console.log("this.citylist2:", this.citylist2)
            }

            this.$refs.refPopup.open('bottom');

        },
        //滚动时禁用确定按钮
        onPickstart() {
            this.changePopup = false;
        },
        //滚动结束
        onPickend() {
            this.changePopup = true;
        },
        //选择值改变事件
        bindChange(e) {

            const { value } = e.detail; // 获取用户选择的值
            // 更新选中值
            console.log('选择值改变事件', value, e.detail.value)
            console.log('选择值改变事件2', e)
            this.region = e.detail.value;
            this.changePopup = true;
        },
        //取消
        onCancel: function () {
            this.$refs.refPopup.close();
            this.changePopup = true;
        },
        //确定
        onSubmit: function () {
            if(this.changePopup == false){
                uni.showToast({
                    title: '变化中,请稍候...',
                    icon: 'none'
                })
                return
            }
            if (this.lastPopup == 'era') {
                const regionText = [{ txt: '70后', num: 70 },
                { txt: '80后', num: 80 },
                { txt: '90后', num: 90 },
                { txt: '00后', num: 0 }]

                this.selEraText = regionText[this.region[0]].txt
                this.selEra = regionText[this.region[0]].num
                console.log("选择年龄段:", this.region, this.selEraText)
                this.$emit('submit-event', { type: 'era', value: this.selEraText, num: this.selEra, text: this.selEraText });
            } else if (this.lastPopup == 'gender') {
                this.selGender = this.region[0] == 0 ? '女' : '男'
                console.log("选择性别:", this.selGender)
                this.$emit('submit-event', { type: 'gender', value: this.selGender });
            } else {
                console.log("选择城市:", this.citylist[this.region[0]])
                const region = this.citylist[this.region[0]]
                this.selRegion = region.label

                const city = region.children?.[this.region[1]]
                this.selCity = city?.label

                const district = city?.children?.[this.region[2]]
                this.selDistrict = district?.label

                this.citytext = `${this.selRegion} ${this.selCity || ''} ${this.selDistrict || ''}`
                this.$emit('submit-event', {
                    type: 'city',
                    value: this.citytext,
                    region: this.selRegion,
                    city: this.selCity,
                    district: this.selDistrict
                });
            }
            this.$refs.refPopup.close(); //关闭地址弹出层
        },
    }
}
</script>
<style lang="scss">
.datePopup-box {
    background-color: #1A1F27;

    .popup-content {
        border-radius: 28rpx 28rpx 0rpx 0rpx;
        width: 100%;
        background-color: #1A1F27;
        color: #D5E4FF;

        .popup-title {
            padding-left: 60upx;
            padding-top: 60upx;
            font-size: 30rpx;
        }

        .timePicker {
            padding: 2px;
            width: 670rpx;
            margin: 48rpx auto 0;
            height: 500rpx;
            background-color: #1A1F27;
            color: #D5E4FF;

            .picker-indicator-class {
                color: #D5E4FF;
            }

            .picker-indicator-class::after {
                border: none;
            }

            .picker-indicator-class::before {
                border: none;
            }

            .item .picker-indicator-class {
                background: #f5f6f7;
                z-index: -1;
            }

            .item1-s .picker-indicator-class {
                width: 98%;
                border-radius: 16rpx 16rpx 16rpx 16rpx;
                border: 1rpx solid #D5E4FF;
                opacity: 0.2;
            }

            .item1 .picker-indicator-class {
                border-radius: 16rpx 0 0 16rpx;
                border: 1rpx solid #D5E4FF;
                opacity: 0.2;
            }

            .item2 .picker-indicator-class {
                border: 1rpx solid #D5E4FF;
                border-left: none;
                border-right: none;
                opacity: 0.2;

            }

            .item3 .picker-indicator-class {
                width: 96%;
                border-radius: 0 16rpx 16rpx 0;
                border: 1px solid #D5E4FF;
                border-left: none;
                opacity: 0.2;
            }

            .picker-item {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 34rpx;

            }
        }
    }

    .btn-Block {
        color: white;
        display: flex;
        justify-content: space-between;
        /* 左右按钮居中对齐 */
        padding: 30px 20px;
        /* 可选：设置左右边距 */

        background-color: #1A1F27;

        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 30rpx;
        color: #FFFFFF;
        line-height: 35rpx;
        text-align: center;
        font-style: normal;
        text-transform: none;

        .left-btn {
            width: 309rpx;
            height: 100rpx;
            border-radius: 16rpx 16rpx 16rpx 16rpx;
            border: 1rpx solid #D5E4FF;
            opacity: 0.5;
            line-height: 100rpx;
        }

        .right-btn {
            width: 309rpx;
            height: 100rpx;
            color: #FFFFFF;
            background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
            border-radius: 16rpx 16rpx 16rpx 16rpx;
            line-height: 100rpx;
        }
    }

    .right-bint-disable {
        background: rgba(149, 149, 149, 0.5) !important;
    }

}
</style>