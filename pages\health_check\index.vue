<template>
  <theme-provider>
    <view class="content" :animation="animationData">
      <image class="background" src="@/static/result-1.png" lazy-load=true mode="aspectFill"></image>

      <!-- 主题切换按钮 -->
      <view class="theme-switch-container">
        <theme-switcher :showLabel="false" />
      </view>

      <view class="header">
        <view class="title">体检报告</view>
      </view>

      <view class="grid-container">
        <view class="grid-row">
          <view class="grid-item" @click="navigateTo('myInfo')">
            <view class="icon-wrapper">
              <image class="icon" src="/static/icons/user.png" mode="aspectFit"></image>
            </view>
            <text class="grid-text">我的信息</text>
          </view>

          <view class="grid-item" @click="navigateTo('healthCheck')">
            <view class="icon-wrapper">
              <image class="icon" src="/static/icons/record.png" mode="aspectFit"></image>
            </view>
            <text class="grid-text">体检报告</text>
          </view>
        </view>
        <!-- 
      <view class="grid-row">
        <view class="grid-item" @click="navigateTo('diagnosis')">
          <view class="icon-wrapper">
            <image class="icon" src="/static/icons/stethoscope.png" mode="aspectFit"></image>
          </view>
          <text class="grid-text">诊断情况</text>
        </view>

        <view class="grid-item" @click="navigateTo('prescription')">
          <view class="icon-wrapper">
            <image class="icon" src="/static/icons/prescription.png" mode="aspectFit"></image>
          </view>
          <text class="grid-text">医生处方</text>
        </view>
      </view>

      <view class="grid-row">
        <view class="grid-item" @click="navigateTo('advice')">
          <view class="icon-wrapper">
            <image class="icon" src="/static/icons/advice.png" mode="aspectFit"></image>
          </view>
          <text class="grid-text">医嘱</text>
        </view>

        <view class="grid-item" @click="navigateTo('weightRecord')">
          <view class="icon-wrapper">
            <image class="icon" src="/static/icons/chart.png" mode="aspectFit"></image>
          </view>
          <text class="grid-text">体重记录</text>
        </view> -->
      </view>
    </view>

    <view class="footer">
      <text class="copyright" @click="navigateTo('ads')">Copyright © 2025 渐随未济健康</text>
    </view>
  </theme-provider>
</template>

<script>
import myStorage from '@/common/my-storage';
import ThemeProvider from '@/components/common/theme-provider.vue';
import ThemeSwitcher from '@/components/common/theme-switcher.vue';


export default {
  components: {
    ThemeProvider,
    ThemeSwitcher
  },
  data() {
    return {
      userInfo: {},
      animationData: {}, // 添加动画数据属性
      menuItems: [
        { id: 'myInfo', name: '我的信息', icon: '/static/icons/user.png' },
        { id: 'medicalRecord', name: '电子病历', icon: '/static/icons/record.png' },
        { id: 'diagnosis', name: '诊断情况', icon: '/static/icons/stethoscope.png' },
        { id: 'prescription', name: '医生处方', icon: '/static/icons/prescription.png' },
        { id: 'advice', name: '医嘱', icon: '/static/icons/advice.png' },
        { id: 'weightRecord', name: '体重记录', icon: '/static/icons/chart.png' }
      ]
    }
  },
  onLoad() {
    this.userInfo = myStorage.getUserInfo() || {}
    console.log("患者工作台 onLoad userInfo:", this.userInfo)
  },
  methods: {
    navigateTo(moduleId) {
      console.log(`导航到模块: ${moduleId}`)

      // 根据不同的模块ID导航到不同的页面
      switch (moduleId) {
        case 'myInfo':
          this.navigateWithAnimation('/pages/mine/index?from=health_check');
          break
        case 'healthCheck':
          this.navigateWithAnimation('/pages/health_check/report?from=health_check');
          break
        case 'medicalRecord':
          this.showModuleMessage('电子病历')
          break
        case 'diagnosis':
          this.showModuleMessage('诊断情况')
          break
        case 'prescription':
          this.showModuleMessage('医生处方')
          break
        case 'advice':
          this.showModuleMessage('医嘱')
          break
        case 'weightRecord':
          this.showModuleMessage('体重记录')
          break
        case 'ads':
          console.log('导航到广告页面')
          this.navigateWithAnimation('/pages/ads/index')
          break
        default:
          this.showModuleMessage('未知模块')
      }
    },

    showModuleMessage(moduleName) {
      uni.showToast({
        title: `${moduleName}功能即将上线`,
        icon: 'none',
        duration: 2000
      })
    },

    // 带淡入淡出动画效果的页面跳转，避免白屏 - 小程序版本
    navigateWithAnimation(url) {
      // 在小程序环境中，使用动画API而不是DOM操作
      const currentPage = this.$scope;

      // 创建动画实例
      const animation = uni.createAnimation({
        duration: 300,
        timingFunction: 'ease',
      });

      // 设置淡出动画
      animation.opacity(0).step();

      // 应用动画到当前页面
      if (currentPage) {
        this.animationData = animation.export();

        // 等待动画完成后跳转
        setTimeout(() => {
          // 跳转到新页面
          uni.navigateTo({
            url: url,
            animationType: 'fade-in',
            success: () => {
              console.log('页面跳转成功');

              // 创建恢复动画
              const restoreAnimation = uni.createAnimation({
                duration: 0,
                timingFunction: 'step-start',
              });

              // 立即恢复透明度，以便返回时正常显示
              restoreAnimation.opacity(1).step();

              // 应用恢复动画
              setTimeout(() => {
                this.animationData = restoreAnimation.export();
              }, 100);
            }
          });
        }, 200);
      } else {
        // 如果无法获取当前页面元素，直接跳转
        uni.navigateTo({
          url: url,
          animationType: 'fade-in'
        });
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  background-color: $primary-color;
  opacity: 0.9;
  position: relative;
  transition: opacity 0.3s ease;
  /* 添加过渡效果 */
  will-change: opacity;
  /* 优化性能 */
}

.background {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: $bg-overlay;
  opacity: 0.08;
  z-index: 1;
}

/* 湖竹主题下的背景图片样式 */
[data-theme="lake_bamboo"] .background {
  opacity: 0.05;
  /* 降低背景图片透明度，减少对文字的干扰 */
}

.header {
  width: 100%;
  padding: 70rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: $text-primary;
  letter-spacing: 4rpx;
  text-align: center;
}

.grid-container {
  flex: 1;
  width: 100%;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  z-index: 2;
  box-sizing: border-box;
}

.grid-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30rpx;
  width: 100%;
}

.grid-item {
  width: 48%;
  height: 200rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);

  &:active {
    transform: scale(0.98);
    background-color: rgba(255, 255, 255, 0.15);
  }
}

/* 湖竹主题下的网格项样式 */
[data-theme="lake_bamboo"] .grid-item {
  background-color: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);

  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
}

.iconfont {
  font-size: 50rpx;
  color: $text-primary;
}

.icon {
  width: 60rpx;
  height: 60rpx;
  filter: brightness(0) invert(1);
  /* 默认为白色 */
}

/* 湖竹主题下的图标样式 */
[data-theme="lake_bamboo"] .icon {
  filter: brightness(0.2);
  /* 湖竹主题下显示为深色 */
}

.grid-text {
  font-size: 28rpx;
  color: $text-primary;
  font-weight: 500;
  letter-spacing: 2rpx;
}

.footer {
  width: 100%;
  padding: 30rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: auto;
  z-index: 2;
}

.copyright {
  font-size: 24rpx;
  color: $text-secondary;
  letter-spacing: 1rpx;
}

// 主题切换按钮样式
.theme-switch-container {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 50rpx;
  padding: 10rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
