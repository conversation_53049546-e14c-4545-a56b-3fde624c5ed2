<script>
import {
	config
} from './config.js'
import {
	AppModel
} from './models/app.js'

import myStorage from "@/common/my-storage"
import themeManager from '@/common/theme-manager'
const appModel = new AppModel()

export default {
	data() {
		return {
		}
	},
	onLaunch: function (option) {
		console.log('App Launch', option)
		// 初始化主题
		this.initTheme()
	},

	onShow: function () {
		console.log('on show');
	},
	onThemeChange: function (options) {
		console.log(options, '系统主题');
	},
	methods: {
		// 初始化主题
		initTheme() {
			// 从 store 初始化主题
			this.$store.dispatch('theme/initTheme');
		}
	}
}
</script>

<style lang="scss">
/* 全局按钮样式 */
button {
  position: relative;
  border: none !important;
  outline: none;
  background-clip: padding-box;
  -webkit-appearance: none;
  -webkit-tap-highlight-color: transparent;
}

button::after {
  border: none !important;
  outline: none;
}
/* 暗黑模式下应用的样式 */
@media (prefers-color-scheme: dark) {
	page {
		background-color: $uni-color-primary;
	}
}

/*每个页面公共css */
/* 小程序中，只支持base64格式的字体 */
/* @font-face {
    font-family: my-font;
    src: url(data:application/font-ttf;charset=utf-8;base64,生成出来的base64字符串) format('truetype');
} */
@font-face {
	font-family: 'font2';
	/* 自定义字体名称 */
	src: url(data:application/font-ttf;charset=utf-8;base64,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) format('truetype');
	/* 字体文件路径 */
}

/* 引入字体2 */
/* @font-face {
	font-family: 'Poetsen One';
	src: url('/static/fonts/PoetsenOne-400.ttf') format('truetype');
} */
</style>
