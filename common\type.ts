type IMyKnowledge = {
  sourceName: string;
  chatId: string;
  title: string;
  content: string;
  userName: string;
};
type EthAddress = string;
type ISessionInfo = {
  sessionId: string;
  expiresAt: string;
};
type ITotalInfoUser = {
  account: EthAddress;
  updatedAt: Date;
  mySendMsg: number;
  sendToMe: number;
  unRead: number;
  myAllMsg: number;
  myKnowledge: number;
  myReply: number;
  mySubject: number;
  balanceAITokens: number; // 我的AI代币余额
  usedAITokens: number; // 我的AI代币消耗
  totalAITokens: number; // 我的AI代币总数
};
type IUserInfo = {
  userId: string; //GroupX ID
  userObjectId: string; //GroupX ID
  sub: string; //SSO ID
  openid: string; // WEIXIN ID
  account: EthAddress;
  email?: string;
  
  name: string;
  avatarUrl: string;
  phoneInfo:any;
  //------------------------------
  // 额外的用户信息
  gender?: string;
  age?: string;
  country?: string;
  intro?: string; // 简介
  skill?: string; // 擅长
  other?: string; // 擅长
  professionalPhoto?: string; // 职业照片(真实照片)
  professionalName?: string; // 职业名字(真实名字)
  //------------------------------
  permissions: string[]; // 拥有的权限
  roles: string[]; // 所属角色
  platformName?: string;
  //------------------------------
  balanceAITokens: number; // 我的AI代币余额
  usedAITokens: number; // 我的AI代币消耗
  totalAITokens: number; // 我的AI代币总数
};
type IUserInfoGroupX = IUserInfo & {
  professionalPhoto: string;
  professionalName: string;
  gender: string;
  age: number;
  intro: string;
  skill: string;
};


type IBodyCondition = {
  饮食习惯与口腔状况: string;
  水分摄入与皮肤状况: string;
  饮食习惯与饥饿感: string;
  心理状态与压力管理: string;
  [key: string]: string; // 支持扩展其他身体状况
}

type IWeightManagement = {
  initialWeight: number;  // 初始体重
  targetWeight: number;   // 目标体重
  weightLossPeriod: number; // 预期减重周期(月)
  bloodSugar: number; // 血糖水平
  bloodPressure: number; // 血压水平
  cholesterol: number; // 胆固醇水平
}

type IHealthSurvey = {
  objectId?: string; // 对象ID
  account: string;    // 用户账号
  userObjectId?: string; // 用户对象ID
  nickname?: string;   // 昵称
  city?: string;        // 城市
  region: string;       // 省份
  district: string;     // 地区
  gender: 'man' | 'woman'; // 性别
  era: number;          // 年代(如70表示70后)
  bodyCondition: IBodyCondition;
  otherConditions?: string; // 其他状况说明
  weightManagement: IWeightManagement;
  createdAt?: Date;     // 创建时间
  updatedAt?: Date;     // 更新时间
}
type IHealthReport = {
  objectId?: string;
  createdAt?: Date;
  updatedAt?: Date;
  hash?: string; // 数据的哈希值
  agent: EthAddress;
  wxGroupOID: string;
  wxUserOID: string;
  account: EthAddress;
  reportType: string;
  ocrResult: string;
  aiResponse: string;
};
const MSG_TYPE = {
  TEXT: "0",
  IMAGE: "1",
  VOICE: "2",
  LOCATION: "3",
  FILE: "4",
  VIDEO: "5",
  LINK: "6",
  EVENT: "7",
};
const isImageMsg = (type: string) =>
  type === MSG_TYPE.IMAGE || type === "IMAGE";
const isVideoMsg = (type: string) =>
  type === MSG_TYPE.VIDEO || type === "VIDEO";
const isVoiceMsg = (type: string) =>
  type === MSG_TYPE.VOICE || type === "VOICE";
const EthZero = "******************************************";

export default MSG_TYPE;
export {
  MSG_TYPE,
  isImageMsg,
  isVideoMsg,
  isVoiceMsg,
  IMyKnowledge,
  EthAddress,
  ISessionInfo,
  ITotalInfoUser,
  IUserInfo,
  IUserInfoGroupX,
  IBodyCondition,
  IWeightManagement,
  IHealthSurvey,IHealthReport,
  EthZero,
};
