function errorUserSession() {
  uni.removeStorageSync("userInfo")
  const href = window?.location?.href
  const url = "#/"
  console.error("user session id is invalid;href:",href)
  
  uni.removeStorageSync("userInfo")

  if (href?.endsWith("/pages/tabbar/home/<USER>") ||  href?.endsWith("#/") 
  ) {
    console.log("window.location.href:", href)
    return
  }

  uni.showToast({
    title: "用户登录信息过期或失效，请重新登录",
    icon: "none",
    duration: 3000,
  })

  setTimeout(function () {
    window.location.href = url
    //uni.navigateTo({ url: "/#/pages/tarbar/login/login" })
  }, 3000)

  // this.goLogout();
}

function checkResponse(result: any) {
  const recvData = result.data
  if (recvData?.code === 60001) {
    console.log("request result:", recvData)
    errorUserSession()
    return null
  }

  if (result.statusCode !== 200) {
    console.error("checkResponse:", result)
    // uni.showToast({
    //   title: result.data?.errors ?? "请求失败",
    //   duration: 4000,
    //   icon: "none",
    // })
    return null
  }

  return recvData
}
export { checkResponse, errorUserSession }
