import { checkResponse } from "../common/check-response"
import { IMyKnowledge, IUserInfo, ISessionInfo, IUserInfoGroupX } from "../common/type"
import myStorage from "../common/my-storage"
import { makeReqParams } from "../common/make-request"
import * as utils from "../common/util"

type METHOD_TYPE = "GET" | "HEAD" | "POST" | "PUT" | "DELETE" | "TRACE" | "CONNECT"
export class ApiBase {
  user = myStorage.getUserInfo()
  session = myStorage.getSession()
  onLogin() {
    this.user = myStorage.getUserInfo()
    this.session = myStorage.getSession()
    console.log("groupx.onLogin:", this.user, this.session)
  }
  onLogout() {
    myStorage.clearUserInfo()
    myStorage.clearSession()
  }
  gRequest(uri: string, data?: any, method?: METHOD_TYPE) {
    let url = uri?.toLowerCase()
    const isHttp = url.startsWith("http") || url.startsWith("https")
    if (!isHttp) url = utils.getApiHostUrl() + uri

    const requestMethod = method ?? (data ? "POST" : "GET")
    const requestData = data
      ? {
          ...this.session,
          ...makeReqParams(data),
          payload: data
        }
      : undefined

    return new Promise((reslove, reject) => {
      uni.request({
        method: requestMethod,
        url,
        header: {
          "Content-Type": "application/json",
          "session-id": this.session.sessionId          
        },
        data: requestData,

        success: (result) => {
          const recvData = checkResponse(result)
          if (recvData === null) reject({ errors: "请求失败" })
          return reslove(recvData)
        },
        fail: (err) => reject(err),
        complete: () => {},
      })
    })
  }
}
