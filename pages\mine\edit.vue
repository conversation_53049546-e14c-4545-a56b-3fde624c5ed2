<template>
	<view class="content">
		<top-bar :current="0" backgroundColor="#ffffff07" color="#c3daf1" tintColor="#02f32b" @click="onBack"
			title="个人信息" />
		<view class="text-tmp">建设中....</view>
		<view class="sel-content">
			<view class="container1" @click="onClickAvatar('city')">
				<text class="text2">{{ citytext || '修改头像' }}</text>
				<image :src="avatar" class="user-avatar" lazy-load=true mode="aspectFill" />
				<view class="icon-wrapper2">
					>
				</view>
			</view>
			<view class="container2" @click="onClickHeaderBG('gender')">
				<text class="text2">{{ selGender || '修改封面' }}</text>
				<view class="icon-wrapper2">
					>
				</view>
			</view>
			<view class="container2" @click="onClickHeaderImg('era')">
				<text class="text2">{{ selEraText || '修改昵称' }}</text>
				<view class="icon-wrapper2">
					>
				</view>
			</view>
		</view>


	</view>
</template>
<script>
import myStorage from '../../common/my-storage'
import ali from "@/utils/ali/uploadOss";
import { userInfo } from 'os';
import groupx from '@/api/api-groupx';

export default {
	components: {  },
	data() {
		return {
			bg_img: 'https://vivid-public.oss-cn-beijing.aliyuncs.com/bg/home-header.png',
			avatar: 'https://vivid-public.oss-cn-beijing.aliyuncs.com/icon/default_avatar.png',
			userInfo: myStorage.getUserInfo(),
		}
	},
	onReady() {
		this.userInfo = myStorage.getUserInfo()
		if (this.userInfo?.vivid?.avatar) {
			groupx.getAliOssUrl(this.userInfo?.vivid?.avatar)?.then(res => {
				this.avatar = res.url
			})
		}
	},
	methods: {
		onBack(e) {
			console.log("onBack:", e)
			uni.reLaunch({ url: '/pages/mine/home' })
		},
		tabClick(e) {
			console.log("tabClick:", e)
			if (e === 0) {
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		},
		getFileInfo(res) {
			var file = res.tempFiles[0]
			console.log('chooseImage:', file, res)

			const filePath = file.path
			var file = res.tempFilePaths[0]
			// 获取上传的文件后缀，然后后端生成随机 COS 路径地址
			var lastIndex = filePath.lastIndexOf('/')
			const fileName = lastIndex > 0 ? filePath.substring(lastIndex + 1) : filePath

			var lastIndex = filePath.lastIndexOf('.')
			var extName = lastIndex > -1 ? filePath.slice(lastIndex + 1) : ''
			return {
				filePath, fileName, extName
			}
		},
		onClickAvatar() {
			const that = this
			// 从相册或照相机获取照片
			uni.chooseImage({
				count: 1, // 可选择的图片数量，这里设为1，表示只能选择一张图片
				sourceType: ['album', 'camera'], // 可选择的图片来源，'album'表示从相册选择，'camera'表示拍照
				success: function (chooseImageRes) {
					const { fileName, extName, filePath } = that.getFileInfo(chooseImageRes)
					ali.uploadOssFile(fileName, extName, filePath)?.then(res => {
						console.log("uploadOssFile avatar:", res)
						// 可以将临时文件路径用作图片的 src，例如：
						that.avatar = filePath;
						const vivid = { ...that.userInfo.vivid, avatar: res?.path }
						const user = myStorage.setUserInfo({ vivid })
						that.$groupx.updateUser(vivid)
					})
				},
				fail: function (err) {
					// 选择失败后的回调函数，err 包含了错误信息
					console.log('选择图片失败:', err);
				}
			});
		},
		onClickHeaderBG() {
			const that = this
			// 从相册或照相机获取照片
			uni.chooseImage({
				count: 1, // 可选择的图片数量，这里设为1，表示只能选择一张图片
				sourceType: ['album', 'camera'], // 可选择的图片来源，'album'表示从相册选择，'camera'表示拍照
				success: function (chooseImageRes) {
					const { fileName, extName, filePath } = that.getFileInfo(chooseImageRes)
					ali.uploadOssFile(fileName, extName, filePath)?.then(res => {
						console.log("uploadOssFile header BG:", that.userInfo)
						const vivid = { ...that.userInfo.vivid, headerBG: res?.path }
						const user = myStorage.setUserInfo({ vivid })
						that.$groupx.updateUser(vivid)
					})
				},
				fail: function (err) {
					// 选择失败后的回调函数，err 包含了错误信息
					console.log('选择图片失败:', err);
				}
			});
		},
	}
}
</script>

<style lang="scss">
.content {
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
	text-align: center;
	justify-content: center;
	align-items: center;

	
	background-color: #181818;


	.text-tmp {
		position: absolute;
		top: 700rpx;
		font-size: 100rpx;
		z-index: 999;
		color: #ffffff08;
	}

	.sel-content {
		width: 90%;
		height: 100%;
		margin-top: 210upx;

		display: flex;
		flex-direction: column;

		opacity: 0.8;

		.container1 {
			margin-top: 40rpx;
			border-radius: 8px 8px 8px 8px;
			border: 1px solid #D5E4FF3f;

			display: flex;
			align-items: center;
			/* 垂直居中 */
			padding: 26rpx 36rpx;

			.user-avatar {
				background-color: black;
				width: 86rpx;
				height: 86rpx;
				border-radius: 100rpx;
				border: none;

			}

			.text2 {
				flex: 1;
				width: 65px;
				height: 18px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 16px;
				color: #D5E4FFaf;
				line-height: 15px;
				text-align: left;
				font-style: normal;
				text-transform: none;
				/* 文字部分占据剩余空间 */
			}

			.icon-wrapper2 {
				color: #ffffff4f;
				width: 24px;
				height: 24px;
				margin-left: 10rpx;
			}


		}

		.container2 {
			margin-top: 40rpx;

			line-height: 34rpx;
			border-radius: 8px 8px 8px 8px;
			border: 1px solid #D5E4FF3f;

			display: flex;
			align-items: center;
			/* 垂直居中 */
			padding: 26rpx 36rpx;

			.text2 {
				flex: 1;
				width: 65px;
				height: 18px;
				font-family: PingFang SC, PingFang SC;
				font-weight: 400;
				font-size: 16px;
				color: #D5E4FFaf;
				line-height: 15px;
				text-align: left;
				font-style: normal;
				text-transform: none;
				/* 文字部分占据剩余空间 */
			}

			.icon-wrapper2 {
				color: #ffffff4f;
				width: 24px;
				height: 24px;
			}


		}

	}


}
</style>
