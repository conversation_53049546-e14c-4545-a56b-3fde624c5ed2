<template>
	<view class="form-container">
		<image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
		<view class="title">健康科学减重</view>
		<view class="step-indicator">
			<view class="step completed">1</view>
			<view class="step-line completed"></view>
			<view class="step completed">2</view>
			<view class="step-line completed"></view>
			<view class="step active">3</view>
		</view>
		<view class="sub-title">基本信息帮助我们更好地为您提供服务</view>

		<view class="content-section">
			<view class="section-title">个人基本信息</view>
			<view class="input-container">
				<view class="input-row" @click="showpop('city')">
					<text class="input-text">{{ citytext || '请选择城市' }}</text>
					<view class="icon-wrapper">
						<image class="icon" src="@/static/下拉.png"></image>
					</view>
				</view>
				<view class="input-row" @click="showpop('gender')">
					<text class="input-text">{{ selGender || '请选择性别' }}</text>
					<view class="icon-wrapper">
						<image class="icon" src="@/static/下拉.png"></image>
					</view>
				</view>
				<view class="input-row" @click="showpop('era')">
					<text class="input-text">{{ selEraText || '请选择年龄段' }}</text>
					<view class="icon-wrapper">
						<image class="icon" src="@/static/下拉.png"></image>
					</view>
				</view>
			</view>
		</view>

		<view class="datePopup-box">
			<uni-popup id="addressPopup" ref="addressPopup">
				<view class="popup-content">
					<view class="popup-title"> {{ pickerTitle }} </view>
					<picker-view mask-style="background:#2C5B9F; z-index:0;height: 100%;"
						indicator-class="picker-indicator-class" style="width: 90%; height: 200px;" class="timePicker"
						indicator-style="height: 50px;background:#2C5B9F; z-index:0;padding-right: 2px;" :value="region"
						@change="bindChange" @pickstart="onPickstart" @pickend="onPickend">

						<picker-view-column :class='citylist[0].children ? "item item1" : "item item1-s"'>
							<view class="picker-item" v-for="(item, index) in citylist" :key="index" :id="item.label"
								value="item.label">
								{{ item.label }}
							</view>
						</picker-view-column>
						<picker-view-column class="item item2" v-if="citylist[region[0]].children" :id="item.label">
							<view class="picker-item" v-for="(item, index) in citylist[region[0]].children"
								:key="index">
								{{ item.label }}
							</view>
						</picker-view-column>
						<picker-view-column class="item item3" v-if="citylist[region[0]].children" :id="item.label">
							<view class="picker-item"
								v-for="(item, index) in citylist[region[0]].children[region[1]].children" :key="index">
								{{ item.label }}
							</view>
						</picker-view-column>
					</picker-view>
				</view>
				<view class="btn-Block">
					<button class="left-btn " @click="onCancel">取消</button>
					<button class="right-btn " :class="changePopup == false ? 'changePopup' : ''" @click="onSubmit">
						确定</button>

				</view>
			</uni-popup>
		</view>
		<view class="footer">
			<view class="tips-text">填写您的个人信息,帮助我们为您提供更精准的服务</view>
			<button class="next-button" @click="clickNext">完成</button>
		</view>
	</view>
</template>
<script>
import myStorage from '@/common/my-storage'
import { fetchAddressDataFromTencent } from '../../common/address-utils'
export default {
	data() {
		return {
			page_from: '',
			pickerTitle: '选择城市',
			bg_img: require('@/static/640.png'),
			region: [0, 0, 0], //地址每列的index
			citylist: [],
			citylist2: [], // 初始化为空数组，等待异步加载
			citylist_x: [{
				label: '浙江', children: [{ label: '杭州', children: [{ label: '上城' }] }]
			}, {
				label: '四川', children: [{ label: '成都', children: [{ label: '锦里' }] }]
			}, {
				label: '江苏', children: [{ label: '南京', children: [{ label: '夫子庙' }] },
				{ label: '苏州', children: [{ label: '相城' }] },
				{ label: '无锡', children: [{ label: '惠山' }] },
				{ label: '常州', children: [{ label: '钟楼' }, { label: '天宁' }, { label: '青羊' }] },
				{ label: '扬州', children: [{ label: '广陵' }] },
				{ label: '泰州', children: [{ label: '海陵' }] },
				{ label: '宿迁', children: [{ label: '宿城' }] },
				]
			}, {
				label: '广东', children: [{ label: '广州', children: [{ label: '黄浦江' }] }]
			}, {
				label: '山东', children: [{ label: '青岛', children: [{ label: '崂山' }] }]
			}, {
				label: '河南', children: [{ label: '郑州', children: [{ label: '金水' }] }]
			}, {
				label: '湖北', children: [{ label: '武汉', children: [{ label: '洪山' }] }]
			}, {
				label: '河北', children: [{ label: '石家庄', children: [{ label: '新华' }] }]
			}, {
				label: '山西', children: [{ label: '太原', children: [{ label: '迎泽' }] }]
			}],//地址数据，格式为 三级地址全返回
			changePopup: true, //确定按钮状态

			selRegion: '',
			selCity: '',
			selDistrict: '',
			citytext: '',//地址文字

			selGender: '',

			selEraIndex: 0,
			selEraText: '',
			selEra: [],

			lastPopup: '',
			eraOptions: [
				{ label: '40年代', value: 40 },
				{ label: '50年代', value: 50 },
				{ label: '60年代', value: 60 },
				{ label: '70年代', value: 70 },
				{ label: '80后', value: 80 },
				{ label: '90后', value: 90 },
				{ label: '00后', value: 0 },
				{ label: '其他', value: 100 }
			],
		}
	},
	onLoad(options) {
		console.log("set-info-other onLoad options:", options)
		// 从options中获取page_from
		if (options && options.from) {
			this.page_from = options.from
			myStorage.setPageFrom(options.from)
		} else {
			this.page_from = myStorage.getPageFrom()
		}
		console.log("onLoad page_from:", this.page_from, options)
	},
	onReady() {
		// 先获取地址数据，再初始化用户信息
		this.fetchAddressData()
		// 读取 userInfo 并初始化
		const user = myStorage.getUserInfo() || {};
		console.log("user----->", user)

		// 初始化城市
		this.selRegion = user.region || '';
		this.selCity = user.city || '';
		this.selDistrict = user.district || '';
		this.citytext = [this.selRegion, this.selCity, this.selDistrict].filter(Boolean).join(' ');

		// 初始化性别
		if (user.gender !== undefined && user.gender !== null) {
			// 只支持 woman/man
			this.selGender = user.gender === 'woman' ? '女' : '男';
		}

		// 初始化年龄段
		if (user.era !== undefined && user.era !== null) {
			const eraValue = parseInt(user.era);
			if (!isNaN(eraValue)) {
				console.log("eraValue----->", eraValue, user.era)
				// 根据数值找到对应的年代选项
				const eraOption = this.eraOptions.find(option => option.value === eraValue);
				if (eraOption) {
					console.log("eraOption----->", eraOption)
					this.selEraText = eraOption.label;
					this.selEra = eraValue;
				}
			}
		}

		// 反查 region 下标
		if (this.selRegion && this.selCity && this.selDistrict) {
			let provinceIdx = this.citylist2.findIndex(p => p.label === this.selRegion);
			let cityIdx = -1, districtIdx = -1;
			if (provinceIdx !== -1) {
				let cities = this.citylist2[provinceIdx].children || [];
				cityIdx = cities.findIndex(c => c.label === this.selCity);
				if (cityIdx !== -1) {
					let districts = cities[cityIdx].children || [];
					districtIdx = districts.findIndex(d => d.label === this.selDistrict);
				}
			}
			if (provinceIdx !== -1 && cityIdx !== -1 && districtIdx !== -1) {
				this.region = [provinceIdx, cityIdx, districtIdx];
			}
		}

	},
	methods: {
		fetchAddressData() {
			return new Promise((resolve) => {
				// 先尝试从本地存储获取
				const localData = myStorage.getRegionData();
				if (localData) {
					this.citylist2 = localData;
					resolve();
				}

				// 然后从服务器获取最新数据
				fetchAddressDataFromTencent().then(result => {
					if (result && result.length > 0) {
						this.citylist2 = result;
						myStorage.setRegionData(result);
					}
					resolve();
				}).catch(() => {
					// 如果服务器获取失败，使用默认数据
					if (!this.citylist2.length) {
						this.citylist2 = this.citylist_x;
					}
					resolve();
				});
			});
		},

		//弹出地址选择器
		showpop(e) {
			this.lastPopup = e
			console.log('弹出地址选择器', e)
			if (e === 'era') {
				this.citylist = this.eraOptions; // 赋值给citylist
				this.pickerTitle = '选择年龄段'

				// 默认选中"50年代"，即eraOptions的下标为1
				const defaultEraIndex = this.eraOptions.findIndex(item => item.label === '50年代');
				this.region = [defaultEraIndex, 0, 0];
			} else if (e === 'gender') {
				this.citylist = [{ label: '女', value: 'woman' }, { label: '男', value: 'man' }];
				this.pickerTitle = '选择性别';
				// 设置高亮
				this.region = [this.selGender === '女' ? 'woman' : 'man', 0, 0];
			} else {
				this.region = [0, 0, 0]
				this.citylist = this.citylist2
				this.pickerTitle = '选择城市'

			}

			this.$refs.addressPopup.open('bottom');

		},
		//滚动时禁用确定按钮
		onPickstart() {
			this.changePopup = false;
		},
		//滚动结束
		onPickend() {
			this.changePopup = true;
		},
		//选择值改变事件
		bindChange(e) {

			const { value } = e.detail; // 获取用户选择的值
			// 更新选中值
			console.log('选择值改变事件', this.region)
			console.log('选择值改变事件2', e)
			this.region = e.detail.value;
			this.changePopup = true;
			console.log('选择值改变事件3', this.region)
		},
		//取消
		onCancel: function () {
			this.$refs.addressPopup.close();
			this.changePopup = true;
		},
		//确定
		onSubmit: function () {
			if (this.changePopup == false) {
				uni.showToast({
					title: '变化中,请稍候...',
					icon: 'none'
				})
				return
			}
			if (this.lastPopup == 'era') {
				console.log("选择年龄段:", this.region)
				const selectedEra = this.eraOptions[this.region[0]];
				this.selEraText = selectedEra.label;
				this.selEra = selectedEra.value;
				console.log("选择年龄段:", this.selEraText)
			} else if (this.lastPopup == 'gender') {
				this.selGender = this.region[0] == 0 ? '女' : '男'
				console.log("选择性别:", this.selGender)
			} else {
				console.log("选择城市:", this.citylist[this.region[0]])
				const region = this.citylist[this.region[0]]
				this.selRegion = region.label

				const city = region.children?.[this.region[1]]
				this.selCity = city?.label

				const district = city?.children?.[this.region[2]]
				this.selDistrict = district?.label

				this.citytext = `${this.selRegion} ${this.selCity || ''} ${this.selDistrict || ''}`
			}
			this.$refs.addressPopup.close(); //关闭地址弹出层
		},
		clickNext() {
			if (!this.selRegion && !this.selCity && !this.selDistrict) {
				uni.showToast({ title: '请选择城市', icon: 'none' })
				return
			}
			if (this.selGender == '') {
				uni.showToast({ title: '请选择性别', icon: 'none' })
				return
			}
			if (this.selEraText == '') {
				uni.showToast({ title: '请选择年龄段', icon: 'none' })
				return
			}

			const user = myStorage.getUserInfo() || {};
			const gender = this.selGender == '女' ? 'woman' : 'man';

			// 更新用户信息
			const updatedUser = {
				...user,
				gender,
				city: this.selCity,
				region: this.selRegion,
				district: this.selDistrict,
				era: this.selEra,
				health: {
					...user.health,
					city: this.selCity,
					region: this.selRegion,
					district: this.selDistrict,
					gender,
					era: this.selEra
				}
			};

			myStorage.setUserInfo(updatedUser);

			console.log("跳转来源 page_from:", this.page_from)
			if (this.page_from == 'health_check') {
				uni.reLaunch({ url: '/pages/health_check/index' });
			} else {
				uni.reLaunch({ url: '/pages/mine/match-friends' });
			}
		}
	}
}
</script>

<style lang="scss">
@import '@/styles/common-form.scss';

// 自定义样式
.input-container {
	width: 100%;
	display: flex;
	flex-direction: column;
	margin-top: 10rpx;
}

.input-row {
	margin-top: 15rpx;
	line-height: 34rpx;
	border-radius: 8px;
	border: 1px solid rgba(255, 255, 255, 0.3);
	display: flex;
	align-items: center;
	padding: 20rpx 30rpx;
	background-color: rgba(255, 255, 255, 0.1);

	&:first-child {
		margin-top: 0;
	}
}

.input-text {
	flex: 1;
	font-size: 28rpx;
	color: $text-primary;
	text-align: left;
	font-weight: 400;

	&:empty::before {
		content: attr(placeholder);
		color: rgba(255, 255, 0, 0.6);
	}
}

.icon-wrapper {
	width: 24px;
	height: 24px;
	display: flex;
	justify-content: center;
	align-items: center;
}

.icon {
	width: 80%;
	height: 80%;
}

.datePopup-box {
	background-color: $primary-color;

	.popup-content {
		border-radius: 28rpx 28rpx 0rpx 0rpx;
		width: 100%;
		background-color: $primary-color;
		color: $text-primary;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);

		.popup-title {
			padding-left: 60upx;
			padding-top: 60upx;
			font-size: 32rpx;
			font-weight: 500;
			color: $text-primary;
			letter-spacing: 2rpx;
		}

		.timePicker {
			padding: 2px;
			width: 670rpx;
			margin: 48rpx auto 0;
			height: 500rpx;
			background-color: #2C5B9F;
			color: $text-primary;
			border-radius: 16rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

			.picker-indicator-class {
				height: 50px !important;
				color: $text-primary;
				background-color: #89B46F !important;
				box-shadow: 0 2rpx 8rpx rgba(137, 180, 111, 0.3);
			}

			.picker-item {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
				color: $text-primary;
				padding: 0 20rpx;
				height: 50px;
				line-height: 50px;
				border-radius: 8rpx;
				transition: all 0.3s ease;
			}

			.picker-view-column {
				.picker-item {
					&.active {
						background-color: #89B46F !important;
						font-weight: 500;
						color: #FFFFFF !important;
					}
				}
			}
		}
	}

	.btn-Block {
		display: flex;
		justify-content: space-between;
		padding: 30px 20px;
		background-color: $primary-color;
		font-size: 32rpx;
		color: $text-primary;
		text-align: center;

		.left-btn {
			width: 309rpx;
			height: 88rpx;
			border-radius: 44rpx;
			border: 1rpx solid rgba(255, 255, 255, 0.3);
			background-color: transparent;
			color: $text-primary;
			font-weight: 500;
			line-height: 88rpx;
			transition: all 0.3s ease;

			&:active {
				background-color: rgba(255, 255, 255, 0.1);
				transform: scale(0.98);
			}
		}

		.right-btn {
			width: 309rpx;
			height: 88rpx;
			color: $text-primary;
			background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
			border-radius: 44rpx;
			line-height: 88rpx;
			font-weight: 500;
			box-shadow: 0 4rpx 12rpx rgba(59, 126, 248, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
				box-shadow: 0 2rpx 8rpx rgba(59, 126, 248, 0.2);
			}
		}
	}

	.changePopup {
		background: rgba(116, 165, 255, 0.5) !important;
		transform: scale(0.98);
	}

}

// 已经在公共样式中定义了按钮样式</style>
