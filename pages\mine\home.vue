<template>
	<view class="content">
		<view class="background2"></view>
		<view class="text-tmp">建设中....</view>
		<image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>

		<view class="background-mask"></view>
		<image :src="avatar" class="user-avatar" lazy-load=true mode="aspectFill" />

		<view class="text-content">
			<text class="text-title">{{ getName }}</text>
			<view class="text-sub-title">
				<view>{{ getCity }}</view>
				<view>|</view>
				<view> {{ getEra }} </view>
			</view>
			<view class="btn-edit" @click="onClickEdit">编辑资料</view>
		</view>

		<view class="bar-tag-info">
			<barTagInfo @submit-event="submitEvent" @goHome-event="goHome" :vivid="userInfo.vivid" />
		</view>

		<view class="bar-level-icon">
			<image src="https://vivid-public.oss-cn-beijing.aliyuncs.com/icon/Medal_01.png" class="level-icon"
				lazy-load=true mode="aspectFill" />
			<image src="https://vivid-public.oss-cn-beijing.aliyuncs.com/icon/Medal_02.png" class="level-icon"
				lazy-load=true mode="aspectFill" />
			<image src="https://vivid-public.oss-cn-beijing.aliyuncs.com/icon/Medal_03.png" class="level-icon"
				lazy-load=true mode="aspectFill" />
		</view>
		<view class="sel-content">
			<view class="container2" @click="onClickTagMan">
				<text class="text2">{{ citytext || '标签管理' }}</text>
				<view>
					<view class="icon-wrapper2">
						<uni-icons type="forward" size="26"></uni-icons>
					</view>
				</view>
			</view>
			<view class="container2" @click="showpop('gender')">
				<text class="text2">{{ selGender || '社交勋章' }}</text>
				<view class="icon-wrapper2">
					<uni-icons type="forward" size="26"></uni-icons>
				</view>
			</view>
			<view class="container2" @click="onClickMessage">
				<text class="text2">{{ selEraText || '我的消息' }}</text>
				<view class="icon-wrapper2">
					<uni-icons type="forward" size="26"></uni-icons>
				</view>
			</view>
		</view>

		<bottom-tab-bar :current="3" backgroundColor="#ffffff00" color="#c3daf1" tintColor="#02f32b"
			@click="tabClick" />
	</view>
</template>
<script>
import barTagInfo from '@/components/cardFriend/barTagInfo';
import myStorage from '../../common/my-storage';
import { shortenStringEnd } from '../../common/util';
import groupx from '@/api/api-groupx';
export default {
	components: { barTagInfo },
	data() {
		return {
			bg_img: '',
			avatar: '',
			userInfo: myStorage.getUserInfo()
		}
	},
	computed: {
	},
	onReady() {
		const that = this
		// 其他页面跳转回来时，重新获取用户信息
		this.userInfo = myStorage.getUserInfo()
		if (this.userInfo?.vivid?.headerBG) {
			this.getAliOssUrl(this.userInfo?.vivid?.headerBG)?.then(res => {
				that.userInfo = myStorage.setUserInfo({ vivid: { ...that.userInfo.vivid, headerBGUrl: res?.url } })
				that.bg_img = res?.url
			})
		} else that.bg_img = 'https://vivid-public.oss-cn-beijing.aliyuncs.com/bg/home-header.png'
		if (this.userInfo?.vivid?.avatar) {
			this.getAliOssUrl(this.userInfo?.vivid?.avatar)?.then(res => {
				that.userInfo = myStorage.setUserInfo({ vivid: { ...that.userInfo.vivid, avatarUrl: res?.url } })
				that.avatar = res?.url
			})
		} else that.avatar = 'https://vivid-public.oss-cn-beijing.aliyuncs.com/icon/default_avatar.png'

		console.log('onReady', that.userInfo, that);
	},
	computed: {
		getEra() { return `${this.userInfo?.vivid?.era.toString().padStart(2, '0')}后` },
		getCity() { return this.userInfo?.vivid?.city }, getName() { return shortenStringEnd(this.userInfo?.vivid?.name || this.userInfo?.name, 16) },
	},
	methods: {

		tabClick(e) {
			console.log("tabClick:", e)
			if (e === 0) {
				uni.reLaunch({ url: '/pages/puke/index' })
			}

		},
		onClickEdit() {
			uni.reLaunch({ url: '/pages/mine/edit' })
		},
		onClickMessage() {
			uni.reLaunch({ url: '/pages/mine/message' })
		},
		onClickTagMan() {
			uni.reLaunch({ url: '/pages/health_weight_loss/health_weight_loss' })
		},
		getAliOssUrl(fileId) { return groupx.getAliOssUrl(fileId) }

	}
}
</script>

<style lang="scss">
.content {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;

  .text-tmp {
    position: absolute;
    top: 880rpx;
    font-size: 100rpx;
    z-index: 999;
    color: #f5f5f5;
  }

  .user-avatar {
    margin-top: 266rpx;
    width: 160rpx;
    height: 160rpx;
    border-radius: 100rpx;
    border: 2rpx solid #FF4D4D;
    box-shadow: 0 6px 12px rgba(255, 77, 77, 0.1);
  }

  .background {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background-color: #f5f5f5;
    opacity: 0.08;
  }

  .background2 {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    background-color: #ffffff;
  }

  .background-mask {
    width: 100%;
    height: 700rpx;
    position: absolute;
    top: 9%;
    left: 0;
    z-index: -1;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 85%);
  }

  .text-content {
    width: 90%;
    margin-top: 30rpx;
    color: #333333;
    display: flex;
    flex-direction: column;
    align-items: center;

    .text-title {
      height: 50rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 42rpx;
      text-align: center;
    }

    .text-sub-title {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      margin-top: 10rpx;
      width: 142rpx;
      height: 36rpx;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 30rpx;
      text-align: center;
    }

    .btn-edit {
      position: fixed;
      top: 26%;
      right: 40rpx;
      width: 132rpx;
      height: 44rpx;
      line-height: 44rpx;
      background: linear-gradient(90deg, #FF4D4D 0%, #FF0080 100%);
      border-radius: 8rpx;
      border: none;
      box-shadow: 0 6px 12px rgba(255, 77, 77, 0.3);
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 12px;
      color: white;
      text-align: center;
    }
  }

  .bar-tag-info {
    width: 90%;
    margin-top: 40rpx;
  }

  .bar-level-icon {
    width: 66%;
    margin-top: 36rpx;
    display: flex;
    justify-content: space-evenly;

    .level-icon {
      width: 100rpx;
      height: 100rpx;
      border-radius: 100%;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }

  .sel-content {
    width: 90%;
    margin-top: 6rpx;
    display: flex;
    flex-direction: column;
    padding: 18rpx 36rpx;

    .container2 {
      margin-top: 28rpx;
      border-radius: 8px;
      background: #ffffff;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 18rpx 20rpx;

      .text2 {
        flex: 1;
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #333333;
        line-height: 15px;
        text-align: left;
      }

      .icon-wrapper2 {
        color: #FF4D4D;
      }
    }
  }
}
</style>
