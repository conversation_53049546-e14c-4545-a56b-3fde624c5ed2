<template>
	<view class="mine-container">
		<header class="header-section">
			<view class="nav-back" @click="handleBack">
				<text class="iconfont icon-back"></text>
			</view>
			<view class="view-header_root" v-if="isLoggedIn">
				<image class="avatar-img" :src="userInfo.avatarUrl || userInfo.avatar || defaultAvatar"></image>
				<view class="user-info">
					<view class="user-info-row">
						<view class="nickname-container">
							<input type="nickname" placeholder='点击选择微信昵称' :value="nickName" @change="onNickNameChange"
								@input="onNickNameInput" class="get-nickname-btn"></input>
						</view>
						<text class="hint-text">点击昵称可进行更新(从底部菜单选择)</text>

					</view>
					<text class="ai-token-info" :class="{ 'negative-balance': (userInfo.balanceAITokens || 0) < 0 }">
						积分余额: {{ userInfo.balanceAITokens || 0 }}
					</text>
				</view>
			</view>
			<view class="view-header_root" v-else>
				<userInfoBtn @onClickBtn="onGetAuthData">
					<section class="un-auth">
						<view>
							<image class="avatar-img" :src="defaultAvatar"></image>
						</view>
						<text>未登录</text>
					</section>
				</userInfoBtn>
			</view>
		</header>

		<view class="user-detail-section" v-if="isLoggedIn">
			<view class="detail-card">

				<view class="detail-item">
					<text class="iconfont icon-phone"></text>
					<view class="detail-flex">
						<text class="detail-text">{{ phoneInfoText }}</text>
						<button class="mini-btn " open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">{{
							isHavePhone() ?
								'更新手机号' : '绑定手机号' }}</button>
					</view>
				</view>
				<view class="detail-item" v-for="item in infoList" :key="item.label"
					v-if="item.label !== 'phone' && item.label !== 'userName'">
					<text :class="item.icon"></text>
					<text class="detail-text">{{ item.text }}</text>
				</view>
			</view>
		</view>

		<view class="weight-management-section"
			v-if="isLoggedIn && userInfo.healthData && userInfo.healthData.weightManagement">
			<view class="detail-card">
				<view class="section-header">
					<text class="section-title">体重管理信息</text>
					<text class="weight-period">减重周期: {{ userInfo.healthData.weightManagement.weightLossPeriod
					}}个月</text>
				</view>
				<view class="detail-item">
					<text class="iconfont icon-weight"></text>
					<view class="detail-flex weight-info">
						<text class="initial-weight">初始体重: <text class="value-number">{{
							userInfo.healthData.weightManagement.initialWeight || '--' }}</text><text
								class="value-unit">kg</text></text>
						<text class="target-weight">目标体重: <text class="value-number">{{
							userInfo.healthData.weightManagement.targetWeight || '--' }}</text><text
								class="value-unit">kg</text></text>
					</view>
				</view>
				<view class="detail-item">
					<text class="iconfont icon-health"></text>
					<view class="detail-flex weight-info">
						<text class="health-value">血糖: <text class="value-number">{{ healthValues.bloodSugar.value
						}}</text><text class="value-unit">{{ healthValues.bloodSugar.unit }}</text></text>
						<text v-if="healthValues.bloodSugar.value !== '--'"
							:class="['health-status', healthValues.bloodSugar.isAbnormal ? 'abnormal' : '']">
							{{ healthValues.bloodSugar.isAbnormal ? '异常' : '正常' }}
						</text>
					</view>
				</view>
				<text class="input-hint">正常范围: <text class="range-number">{{ healthValues.bloodSugar.range }}</text> {{
					healthValues.bloodSugar.unit }}</text>
				<view class="detail-item">
					<text class="iconfont icon-heart"></text>
					<view class="detail-flex weight-info">
						<text class="health-value">血压: <text class="value-number">{{ healthValues.bloodPressure.value
						}}</text><text class="value-unit">{{ healthValues.bloodPressure.unit }}</text></text>
						<text v-if="healthValues.bloodPressure.value !== '--'"
							:class="['health-status', healthValues.bloodPressure.isAbnormal ? 'abnormal' : '']">
							{{ healthValues.bloodPressure.isAbnormal ? '异常' : '正常' }}
						</text>
					</view>
				</view>
				<text class="input-hint">正常范围: <text class="range-number">{{ healthValues.bloodPressure.range }}</text>
					{{
						healthValues.bloodPressure.unit }}</text>
				<view class="detail-item">
					<text class="iconfont icon-cholesterol"></text>
					<view class="detail-flex weight-info">
						<text class="health-value">胆固醇: <text class="value-number">{{ healthValues.cholesterol.value
						}}</text><text class="value-unit">{{ healthValues.cholesterol.unit }}</text></text>
						<text v-if="healthValues.cholesterol.value !== '--'"
							:class="['health-status', healthValues.cholesterol.isAbnormal ? 'abnormal' : '']">
							{{ healthValues.cholesterol.isAbnormal ? '异常' : '正常' }}
						</text>
					</view>
				</view>
				<text class="input-hint">正常范围: <text class="range-number">{{ healthValues.cholesterol.range }}</text> {{
					healthValues.cholesterol.unit }}</text>
			</view>
		</view>

		<main>
			<view class="btm-cnt">
				<view class="view-item-container" v-for="(item, index) in cellDataArray" :key="index"
					@tap="jumpToLoveRecord(item)">
					<text :class="item.img"></text>
					<text class="text-item-title">{{ item.title }}</text>
				</view>
				<view class="view-item-container" @tap="logoutFansTeam">
					<text class="icon-font icon-fans-right-arrow"></text>
					<text class="text-item-title">退出登录</text>
				</view>
			</view>
		</main>

		<neil-modal :auto-close="false" :show="logout" :confirmColor="'#F73657'" @cancel="bindBtnCancel"
			@confirm="bindBtnExit" :confirmText="'确认'" :cancelText="'取消'">
			<view class="logout-modal-content">
				<view class="logout-icon">
					<text class="iconfont icon-warning"></text>
				</view>
				<view class="logout-title">确定要退出登录吗？</view>
			</view>
		</neil-modal>
	</view>
</template>

<script>
import neilModal from '@/components/neil-modal/neil-modal.vue'
import userInfoBtn from '@/components/common/userInfoBtn.vue'
import { mapGetters } from 'vuex'
import myStorage from "@/common/my-storage"
import groupx from "@/api/api-groupx";
import { autoLoginAndGetUserInfo } from '@/common/auth'

export default {
	onLoad(options) {

		console.log("mine onLoad----》", options)
		if (options && options.from) {
			this.page_from = options.from
		}
		console.log("mine onload:", this.userInfo)
	},
	onReady() {
		this.userInfo = myStorage.getUserInfo()
		this.nickName = this.userInfo.nickName
		this.refreshWxLoginCode();
	},
	onShow() {
		console.log("mine onShow", this.userInfo?.nickName)
		if (!this.userInfo?.account) {
			console.log("自动登录")
			autoLoginAndGetUserInfo().then(userInfo => {
				console.log("自动登录成功", userInfo)
				this.userInfo = userInfo
				myStorage.setUserInfo(userInfo)
				this.nickName = userInfo.nickName

				this.loadHealthSurvey()

				if (!this.nickName) {
					uni.showToast({
						title: '请点击页面上方昵称，获取微信昵称',
						icon: 'none'
					})
				}
			}).catch(err => {
				console.error('自动登录失败:', err)
			})
		}

		if (!this.userInfo?.healthData && this.userInfo?.account) {
			this.loadHealthSurvey()
		}

		console.log("mine onShow", this.userInfo?.account)
	},
	components: {
		neilModal,
		userInfoBtn
	},
	data() {
		return {
			userInfo: myStorage.getUserInfo(),
			nickName: '',// 仅本地可被用户编辑,不更新到服务器
			wxCode: '',
			page_from: 'mine',
			eraInfo: [
				{ label: '40年代', value: 40 },
				{ label: '50年代', value: 50 },
				{ label: '60年代', value: 60 },
				{ label: '70年代', value: 70 },
				{ label: '80后', value: 80 },
				{ label: '90后', value: 90 },
				{ label: '00后', value: 0 },
				{ label: '其他', value: 100 }
			]
			,
			logOutCell: {
				img: 'icon-font icon-fans-right-arrow',
				title: '退出登录',
				url: "/pages/result",
				type: 4
			},
			idolName: "",
			logout: false,
			newNickname: "",
			idolInfo: {},
			starUrl: '',
			userInfo: {},
			defaultAvatar: '/static/icons/user-default.png'
		}
	},
	computed: {
		isLoggedIn() {
			return !!(this.userInfo && (this.userInfo.userName || this.userInfo.name))
		},
		getUserName() {
			return this.userInfo?.nickName || this.userInfo?.name || '未登录'
		},
		getUserAvatar() {
			return this.userInfo?.avatarUrl || this.userInfo?.avatar || this.defaultAvatar
		},
		phoneMask() {
			const phoneInfo = this.userInfo.phoneInfo
			if (phoneInfo) {
				const phone = phoneInfo.phoneNumber
				if (phone) {
					return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2")
				}
			}
			return ""
		},
		phoneInfoText() {
			return this.userInfo.phoneInfo && this.userInfo.phoneInfo.phoneNumber ? this.phoneMask : '未绑定手机号'
		},
		locationText() {
			if (this.userInfo.region || this.userInfo.city || this.userInfo.district) {
				return (this.userInfo.region || '') + ' ' + (this.userInfo.city || '') + ' ' + (this.userInfo.district || '')
			}
			return '未设置地区'
		},
		genderText() {
			if (this.userInfo.gender) {
				return this.userInfo.gender === 'man' ? '男' : '女'
			}
			return '未设置性别'
		},
		eraText() {
			const era = this.eraInfo.find(item => item.value === this.userInfo.era)
			return era ? era.label : ''
		},
		aiTokenText() {
			return this.userInfo.balanceAITokens ? 'AI积分余额: ' + this.userInfo.balanceAITokens : 'AI积分余额: 0'
		},
		infoList() {
			return [
				{
					label: 'gender',
					icon: 'iconfont icon-gender',
					text: this.userInfo.era ? `${this.genderText} [${this.eraText}]` : this.genderText
				},
				{ label: 'location', icon: 'iconfont icon-location', text: this.locationText }
			];
		},
		showGetNickNameBtn() {
			const name = this.userInfo.nickName || this.userInfo.name || '';
			return !name || name.startsWith('wechat-');
		},
		cellDataArray() {
			const baseArray = [
				{
					img: 'icon-font icon-fans-edit',
					title: '重新设置我的信息',
					type: 1
				}, {
					img: 'icon-font icon-redeem_icon',
					title: '回到首页',
					type: 2,
				}, {
					img: 'icon-font icon-fans-service',
					title: '联系客服',
					type: 3
				}
			];

			// 如果是从健康检查页面跳转而来，则隐藏"重新设置我的信息"项
			// if (this.from === 'health_check') {
			// 	return baseArray.filter(item => item.type !== 1);
			// }

			return baseArray;
		},
		healthValues() {
			const management = this.userInfo?.healthData?.weightManagement || {};
			return {
				bloodSugar: {
					value: management.bloodSugar || '--',
					isAbnormal: management.bloodSugar ? (management.bloodSugar < 3.9 || management.bloodSugar > 6.1) : false,
					unit: 'mmol/L',
					range: '3.9-6.1'
				},
				bloodPressure: {
					value: management.bloodPressure || '--',
					isAbnormal: management.bloodPressure ? (management.bloodPressure < 90 || management.bloodPressure > 140) : false,
					unit: 'mmHg',
					range: '90-140'
				},
				cholesterol: {
					value: management.cholesterol || '--',
					isAbnormal: management.cholesterol ? (management.cholesterol < 2.8 || management.cholesterol > 5.2) : false,
					unit: 'mmol/L',
					range: '2.8-5.2'
				}
			}
		},
		...mapGetters([
			'userInfo2'
		])
	},

	filters: {
		formatAvatarUrl(val) {
			return val
		},
		formatUserName(val) {
			return val || '未登录'
		}
	},

	methods: {
		handleBack() {
			uni.navigateBack()
		},

		loadHealthSurvey() {
			const that = this
			groupx.getHealthSurvey().then(res => {
				console.log("获取健康调查结果", res)
				if (res.code === 200) {
					that.userInfo.healthData = res.data
					that.userInfo = myStorage.setUserInfo(that.userInfo)
					// const weightManagement = res.data.weightManagement
					// if (weightManagement) {
					// 	// weightManagement: {initialWeight: 87, targetWeight: 77, weightLossPeriod: 3, bloodSugar: 1, bloodPressure: 2,weightLossPeriod:3}
					// }
				}
			})
		},
		logoutFansTeam() {
			this.idolName = this.userInfo.starName;
			this.logout = true;
		},
		onGetAuthData() {
			//获取授权成功后的数据
			console.log("获取授权数据")
		},
		jumpToLoveRecord(item) {
			console.log("跳转到记录", item)
			switch (item.type) {
				case 1:
					console.log("跳转到我的信息")
					const url1 = this.page_from === "health_check"
						? "/pages/mine/set-info-other?from=health_check"
						: "/pages/health_weight_loss/health_weight_loss"
					uni.navigateTo({ url: url1 });
					break;
				case 2:
					const url2 = this.page_from === "health_check" ?
						"/pages/health_check/index" : "/pages/result"
					uni.navigateTo({ url: url2 });
					break;
				case 3:
					uni.openCustomerServiceChat({
						extInfo: { url: 'https://user.js.mfull.cn/customer-service' }, // 替换为实际的客服链接
						corpId: 'wwdcedc77085392ed9', // 替换为实际的企业ID
						success(res) {
							console.log('打开客服会话成功', res);
						},
						fail(res) {
							console.log('打开客服会话失败', res);
						}
					})
					// wx.openCustomerServiceChat({
					// 	extInfo: { url: '您的客服链接' }, // 替换为实际的客服链接
					// 	corpId: '您的企业ID', // 替换为实际的企业ID
					// 	success(res) {
					// 		console.log('打开客服会话成功', res);
					// 	},
					// 	fail(res) {
					// 		console.log('打开客服会话失败', res);
					// 	}
					// });
					break;
				case 4:
					this.showOffical = true;
					break;
			}
		},
		bindBtnCancel() {
			this.logout = false
		},
		bindBtnExit() {
			this.logout = false
			myStorage.clearUserInfo()
			myStorage.clearSession()
			// 退出后返回首页
			uni.reLaunch({
				url: '/pages/ads/index'
			})
		},
		isHavePhone() {
			return (this.userInfo && this.userInfo.phoneInfo?.phoneNumber)
		},
		getPhoneNumber(e) {
			console.log("获取手机号事件：", e)
			if (!e.detail.code) {
				uni.showToast({
					title: '获取手机号失败(未授权)',
					icon: 'none'
				})
				return
			}

			groupx.getWxPhoneNumber(e.detail.code).then(res => {
				console.log("获取手机号结果：", res)
				if (res.code === 200) {
					let phoneInfo = res.data
					if (phoneInfo) {
						const user = {
							...this.userInfo,
							phoneInfo: phoneInfo
						}
						this.userInfo = myStorage.setUserInfo(user)
						// 同步到服务器
						groupx.updateUserInfo(user)
							.then(() => {
								uni.showToast({ title: '手机号同步成功', icon: 'success' })
							})
							.catch(() => {
								uni.showToast({ title: '手机号同步失败', icon: 'none' })
							})
						uni.showToast({
							title: '手机号绑定成功',
							icon: 'success'
						})
					}
				} else {
					uni.showToast({
						title: res.errmsg || '获取手机号失败',
						icon: 'none'
					})
				}
			}).catch(err => {
				console.error('获取手机号失败：', err)
				uni.showToast({
					title: '获取手机号失败，请重试',
					icon: 'none'
				})
			})
		},
		refreshWxLoginCode() {
			wx.login({
				provider: 'weixin',
				onlyAuthorize: true,
				success: (res2) => {
					console.log("获取微信登录code", res2.code)
					if (res2.code) {
						this.wxCode = res2.code
					}
				}
			});
		},
		getWxNickName() {
			// 此方法现在由 input type="nickname" 自动处理，可以暂时保留或根据实际情况移除
			// 如果input的@change事件不足以满足需求，可以考虑在这里处理额外的逻辑
			console.log('getWxNickName called, but nickname input should handle it');
		},
		onNickNameChange(e) {
			console.log('onNickNameChange', e);
			const nickName = e.detail.value;
			console.log('新的昵称:', nickName);

			if (nickName) {
				const user = {
					...this.userInfo,
					nickName: nickName,
				};
				this.userInfo = myStorage.setUserInfo(user);
				groupx.updateUserInfo(user)
					.then(() => {
						uni.showToast({ title: '昵称更新成功', icon: 'success' });
					})
					.catch(() => {
						uni.showToast({ title: '昵称同步失败', icon: 'none' });
					});
			} else {
				uni.showToast({ title: '昵称不能为空', icon: 'none' });
			}
		},
		onNickNameInput(e) {
			console.log("------------------->", e)
			uni.showToast({
				title: '请从底部菜单选择微信昵称,不要手工修改.', icon: 'none',
				duration: 3000,
			});
		}
	}
}
</script>

<style lang="scss" scoped>
@import '@/styles/common-form.scss';
@import '@/styles/variables.scss';

.mine-container {
	header.header-section {
		padding: 40upx 20upx;
		position: relative;

		.nav-back {
			display: inline-block;
			padding: 16upx;
			background: rgba(255, 255, 255, 0.1);
			border-radius: 50%;
			margin-bottom: 20upx;

			.iconfont {
				font-size: 36upx;
				color: #fff;
				line-height: 1;
			}
		}
	}

	background: $primary-color;
	min-height: 100vh;
	padding-bottom: 0;

	>header {
		background: none;
		border-radius: 0;
		min-height: 120upx;
		box-sizing: border-box;
		padding: 32upx 32upx 0 32upx;

		.view-header_root {
			display: flex;
			flex-direction: row;
			align-items: center;
			justify-content: flex-start;
			min-height: 300upx;
			width: 100%;

			.avatar-img {
				width: 100upx;
				height: 100upx;
				border-radius: 50%;
				border: 2upx solid #fff;
				box-shadow: 0 2upx 8upx rgba(0, 0, 0, 0.08);
				flex-shrink: 0;
			}

			.user-info {
				margin-left: 24upx;
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: flex-start;

				.user-info-row {

					>text {
						font-size: 22upx;
						color: rgba(255, 255, 255, 0.6);
						margin-top: 8upx;
						margin-left: 12upx;
					}
				}

				.user-name {
					font-size: 32upx;
					color: #fff;
					font-weight: 600;
					word-break: break-all;
					margin-right: 16upx; // 增加一些右边距，避免和按钮太近
				}

				.get-nickname-btn {
					background: rgba(255, 255, 255, 0.05);
					color: rgba(255, 255, 255, 0.85);
					font-size: 26upx;
					font-weight: 500;
					border-radius: 24upx;
					margin-left: auto;
					padding: 0upx 16upx;
					box-shadow: 0 2upx 8upx rgba(21, 124, 253, 0.08);
					display: inline-flex;
					align-items: center;
					justify-content: center;
					transition: background 0.2s;
				}

				.get-nickname-btn:active {
					background: rgba(21, 124, 253, 0.08);
				}

				.ai-token-info {
					font-size: 24upx;
					color: rgba(255, 255, 255, 0.85);
					margin-top: 8upx;
					background: rgba(255, 255, 255, 0.15);
					padding: 4upx 16upx;
					border-radius: 24upx;
					transition: all 0.3s ease;

					&.negative-balance {
						background: rgba(247, 54, 87, 0.2);
						color: #ff4757;
						border: 1upx solid rgba(247, 54, 87, 0.5);
						font-weight: 600;
						animation: pulse 2s infinite;
					}
				}

				@keyframes pulse {
					0% {
						box-shadow: 0 0 0 0 rgba(247, 54, 87, 0.4);
					}
					70% {
						box-shadow: 0 0 0 10upx rgba(247, 54, 87, 0);
					}
					100% {
						box-shadow: 0 0 0 0 rgba(247, 54, 87, 0);
					}
				}

				.mini-btn {
					margin-left: 0;
					margin-top: 0;
					align-self: flex-start;
				}
			}
		}
	}

	.user-detail-section {
		margin: -8upx 16upx 12upx 16upx;

		.detail-card {
			background-color: rgba(255, 255, 255, 0.15);
			border-radius: 16upx;
			box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.15);
			border: 1px solid rgba(255, 255, 255, 0.25);
			padding: 0;

			.detail-item {
				display: flex;
				align-items: center;
				padding: 24upx 24upx;
				// 移除横向分隔线
				border-bottom: none;

				.iconfont {
					font-size: 32upx;
					color: rgba(255, 255, 255, 0.9);
					margin-right: 20upx;
				}

				.detail-flex {
					display: flex;
					align-items: center;
					flex: 1;
					min-width: 0;

					.detail-text {
						flex: 1;
						min-width: 0;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
						font-size: 28upx;
						color: rgba(255, 255, 255, 0.9);
					}

					.mini-btn {
						margin-left: 16upx;
						background: rgba(21, 124, 253, 0.08);
						color: $text-primary;
						font-size: 24upx;
						padding: 8upx 20upx;
						border-radius: 24upx;
						border: 1px solid $primary-color;
						min-width: 120upx;
						height: 48upx;
						line-height: 48upx;
						box-shadow: 0 2upx 8upx rgba(21, 124, 253, 0.08);
						display: inline-flex;
						align-items: center;
						justify-content: center;
						transition: background 0.2s;
					}
				}

				.detail-text {
					flex: 1;
					min-width: 0;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					font-size: 28upx;
					color: rgba(255, 255, 255, 0.9);
				}
			}
		}
	}

	.weight-management-section {
		margin: 0 16upx 12upx 16upx;

		.section-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 24upx;

			.section-title {
				font-size: 32upx;
				font-weight: 600;
				color: rgba(255, 255, 255, 0.9);
			}

			.weight-period {
				font-size: 28upx;
				color: rgba(255, 255, 255, 0.85);
			}
		}

		.detail-card {
			background-color: rgba(255, 255, 255, 0.15);
			border-radius: 16upx;
			box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.15);
			border: 1px solid rgba(255, 255, 255, 0.25);
			padding: 0upx 20upx 20upx 0upx;

			.input-hint {
				margin-top: -20upx;
				margin-bottom: 16upx;
				padding: 0 24upx;
				font-size: 24upx;
				color: rgba(255, 255, 255, 0.85);
				line-height: 1.2;
				display: block;
				text-align: right;
				width: 100%;
				box-sizing: border-box;

				.range-number {
					color: #fff;
					font-weight: 500;
				}
			}

			.detail-item {
				display: flex;
				align-items: center;
				padding: 24upx 24upx;
				// 移除横向分隔线
				border-bottom: none;
				position: relative;

				.iconfont {
					font-size: 32upx;
					color: rgba(255, 255, 255, 0.9);
					margin-right: 20upx;
				}

				.detail-flex {
					display: flex;
					align-items: center;
					flex: 1;
					min-width: 0;

					&.weight-info {
						justify-content: space-between;
						align-items: center;
						padding: 0 20upx;

						.initial-weight,
						.health-value {
							flex: 0 1 auto;
							min-width: 0;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							font-size: 28upx;
							color: rgba(255, 255, 255, 0.9);
						}

						.target-weight {
							text-align: right;
							flex: 0 1 auto;
							min-width: 0;
							overflow: hidden;
							text-overflow: ellipsis;
							white-space: nowrap;
							font-size: 28upx;
							color: #fff;
							margin-left: auto; // 添加这行确保靠右对齐
						}

						.value-number {
							font-size: 32upx;
							color: #fff;
							font-weight: 600;
							margin: 0 4upx;
						}

						.value-unit {
							font-size: 24upx;
							color: rgba(255, 255, 255, 0.7);
							margin-left: 2upx;
						}

						.health-status {
							flex-shrink: 0;
							font-weight: 500;
							margin-left: 16upx;
							font-size: 28upx;
							color: rgba(255, 255, 255, 0.9);

							&.abnormal {
								color: rgba(247, 54, 87, 0.95);
								font-weight: bold;
							}
						}
					}
				}


			}
		}
	}

	.btm-cnt {
		margin: 0 16upx;
		background-color: rgba(255, 255, 255, 0.15);
		border-radius: 16upx;
		box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.15);
		border: 1px solid rgba(255, 255, 255, 0.25);
		overflow: hidden;

		.view-item-container {
			display: flex;
			align-items: center;
			padding: 24upx 24upx;
			border-bottom: 1px solid #f0f0f0;

			.icon-font {
				font-size: 36upx;
				color: rgba(255, 255, 255, 0.9);
				margin-right: 20upx;
			}

			.text-item-title,
			.button-item-title {
				font-size: 32upx;
				color: rgba(255, 255, 255, 0.9);
				flex: 1;
				text-align: left;
			}


		}

		.view-item-container:last-child {
			border-bottom: none;
		}
	}

	.logout-modal-content {
		background: #fff;
		padding: 40upx 32upx;
		text-align: center;

		.logout-icon {
			margin-bottom: 24upx;

			.iconfont {
				font-size: 80upx;
				color: #F73657;
			}
		}

		.logout-title {
			color: #333;
			font-size: 36upx;
			font-weight: 600;
			margin-bottom: 16upx;
		}

		.logout-desc {
			color: #666;
			font-size: 28upx;
			line-height: 1.5;
		}
	}


}
</style>
