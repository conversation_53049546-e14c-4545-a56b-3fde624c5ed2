<template>
	<view class="user-info-container">
		<button class="user-info-btn" open-type="getUserInfo" @getuserinfo="mpGetUserInfo" withCredentials="true">
			<slot></slot>
		</button>
	</view>
</template>

<script>
import {
	AppModel
} from '@/models/app.js'
import { getApiHostUrl } from "@/common/util.ts";
import { getAgent } from "@/common/util.ts";
import groupx from "@/api/api-groupx"
import myStorage from "@/common/my-storage"
const appModel = new AppModel();

export default {
	name: 'userInfoBtn',
	data() {
		return {
			userInfo: {}
		}
	},
	methods: {
		mpGetUserInfo(result) {
			const that = this
			console.log("-------------login", result)
			console.log("process:", process.env)
			// 微信登录
			wx.login({
				success: function (res) {
					console.log("-------------login2", res)
					if (res.code) {
						const code = res.code
						if (code) {
							console.log("onLoad code:", getApiHostUrl())
							console.log("onLoad code:", code)
							groupx.wxMiniProgramLogin({
								agent: getAgent(),
								platformName: 'GATESSO',
								authData: {
									code,
									scope: "read",
									tag: "wechat_miniprogram"
								}
							})?.then((res) => {
								console.info("微信小程序登录：", res)
								if (res.success) {
									groupx.getUserInfo(res.ssoID)?.then((userinfo) => {
										console.log("group userinfo:", userinfo)
										myStorage.setUserInfo(userinfo)
									})
								}
							}).catch(function (error) {
								console.log(error)

								uni.showToast({
									title: error.code == "ERR_NETWORK" ? "网络错误" : "登录错误",
									icon: "none",
									duration: 5000,
								})

								setTimeout(() => {
									// that.goLogin()
								}, 5000)
							})

						} else {
							const user = myStorage.getUserInfo()
							if (getStringLength(user.openid) > 0) {
								console.log("onLoad userInfo:", user)
								this.login = true
								this.userInfo = user
							}
						}

					} else {
						console.error('登录失败:', res.errMsg);
					}
				},
				fail: function (err) {
					console.error('调用登录接口失败:', err);
				}
			});
		},
	}
}
</script>

<style lang="less" scoped>
.user-info-btn::after {
	border: 0;
}

.user-info-btn {
	background-color: transparent;
	line-height: 0;
	padding: 0;
	font-size: 0;
}
</style>
