<template>
  <view class="theme-switcher">
    <view class="theme-switcher-container" @click="toggleThemeSelector">
      <view class="current-theme">
        <view class="theme-icon" :class="[currentThemeClass]">
          <view class="color-preview primary" :style="{ backgroundColor: primaryColor }"></view>
          <view class="color-preview secondary" :style="{ backgroundColor: secondaryColor }"></view>
        </view>
        <text v-if="showLabel" class="theme-name">{{ themeName }}</text>
      </view>
    </view>

    <!-- 主题选择器弹出层 -->
    <uni-popup ref="themePopup" type="bottom">
      <view class="theme-selector">
        <view class="theme-selector-header">
          <text class="title">选择主题配色</text>
          <view class="close-btn" @click="closeThemeSelector">关闭</view>
        </view>

        <view class="theme-list">
          <!-- 默认主题 -->
          <view
            class="theme-item"
            :class="{ active: isDefaultTheme }"
            @click="switchTheme(THEME_TYPES.DEFAULT)"
          >
            <view class="theme-preview default-theme">
              <view class="color-block" style="background-color: #2C5B9F;"></view>
              <view class="color-block" style="background-color: #89B46F;"></view>
            </view>
            <text class="theme-item-name">默认主题</text>
            <text v-if="isDefaultTheme" class="theme-selected-icon">✓</text>
          </view>

          <!-- 湖竹主题 -->
          <view
            class="theme-item"
            :class="{ active: isLakeBambooTheme }"
            @click="switchTheme(THEME_TYPES.LAKE_BAMBOO)"
          >
            <view class="theme-preview lake-bamboo-theme">
              <view class="color-block" style="background-color: #AFD3DF;"></view>
              <view class="color-block" style="background-color: #BAE0CD;"></view>
            </view>
            <text class="theme-item-name">湖竹主题</text>
            <text v-if="isLakeBambooTheme" class="theme-selected-icon">✓</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import { THEME_TYPES } from '@/common/theme-manager';

export default {
  name: 'ThemeSwitcher',
  props: {
    showLabel: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'medium' // small, medium, large
    }
  },
  data() {
    return {
      THEME_TYPES,
      primaryColor: '',
      secondaryColor: ''
    };
  },
  computed: {
    ...mapGetters('theme', [
      'currentTheme',
      'isDefaultTheme',
      'isLakeBambooTheme',
      'themeName'
    ]),
    currentThemeClass() {
      return `theme-${this.currentTheme}`;
    }
  },
  watch: {
    currentTheme: {
      immediate: true,
      handler(newTheme) {
        this.updateThemeColors(newTheme);
      }
    }
  },
  methods: {
    ...mapActions('theme', [
      'switchTheme'
    ]),
    toggleThemeSelector() {
      this.$refs.themePopup.open();
    },
    closeThemeSelector() {
      this.$refs.themePopup.close();
    },
    updateThemeColors(theme) {
      if (theme === THEME_TYPES.DEFAULT) {
        this.primaryColor = '#2C5B9F';
        this.secondaryColor = '#89B46F';
      } else if (theme === THEME_TYPES.LAKE_BAMBOO) {
        this.primaryColor = '#AFD3DF';
        this.secondaryColor = '#BAE0CD';
      }
    }
  }
};
</script>

<style lang="scss" scoped>
/* 导入变量 */
@import '@/styles/variables.scss';
.theme-switcher {
  display: inline-flex;
  align-items: center;

  &-container {
    cursor: pointer;
    padding: 8rpx;
    border-radius: 8rpx;
    transition: all 0.3s ease;

    &:active {
      opacity: 0.7;
    }
  }

  .current-theme {
    display: flex;
    align-items: center;

    .theme-icon {
      display: flex;
      width: 60rpx;
      height: 30rpx;
      border-radius: 15rpx;
      overflow: hidden;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);

      .color-preview {
        flex: 1;
        height: 100%;
      }
    }

    .theme-name {
      margin-left: 10rpx;
      font-size: 24rpx;
      color: #FFFFFF; /* 使用固定颜色而不是变量 */
    }
  }
}

.theme-selector {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close-btn {
      font-size: 28rpx;
      color: #666;
      padding: 10rpx;
    }
  }

  .theme-list {
    display: flex;
    flex-wrap: wrap;
    gap: 30rpx;

    .theme-item {
      width: 45%;
      padding: 20rpx;
      border-radius: 12rpx;
      background-color: #f8f8f8;
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      transition: all 0.3s ease;

      &.active {
        background-color: #f0f0f0;
        box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
      }

      .theme-preview {
        width: 100%;
        height: 100rpx;
        display: flex;
        border-radius: 8rpx;
        overflow: hidden;
        margin-bottom: 16rpx;

        .color-block {
          flex: 1;
          height: 100%;
        }
      }

      .theme-item-name {
        font-size: 28rpx;
        color: #333;
      }

      .theme-selected-icon {
        position: absolute;
        top: 10rpx;
        right: 10rpx;
        color: #89B46F; /* 使用固定颜色而不是变量 */
        font-size: 40rpx;
        font-weight: bold;
      }
    }
  }
}
</style>
