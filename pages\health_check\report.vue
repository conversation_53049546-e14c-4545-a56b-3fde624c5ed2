<template>
  <view class="mine-container">
    <!-- 顶部空出一定空间 -->
    <view class="top-space"></view>

    <!-- 新增标题栏 -->
    <view class="header">
      <view class="title">体检报告</view>
    </view>

    <view class="result-container">
      <view class="result-text">
        <text class="result-description"
          style="font-size: 32upx; color: rgba(255, 255, 255, 0.85); text-align: left; display: block; margin-bottom: 30rpx;margin-left: 20rpx;">
          {{ aiAnalysis ? '您的体检结果已生成，请查看详细报告。' : '暂无体检报告' }}
        </text>
      </view>
    </view>

    <!-- AI 体检报告总结 -->
    <view class="ai-analysis-section" v-if="aiAnalysis">
      <view class="analysis-card">
        <view class="analysis-header">
          <text class="analysis-title">AI 体检总结与分析</text>
        </view>
        <view class="analysis-content">
          <text>{{ aiAnalysis.summary }}</text>
          <text v-if="aiAnalysis.recommendations" class="recommendation-title">建议:</text>
          <ul class="recommendation-list">
            <li v-for="(rec,idx) in aiAnalysis.recommendations" :key="idx" class="recommendation-item">
              {{ rec }}
            </li>
          </ul>
        </view>
      </view>
    </view>
    <!-- 完善体检报告内容 -->
    <view class="additional-info" v-if="aiAnalysis.otherHealthIndicators">
      <text class="additional-title">其他健康指标:</text>
      <ul class="additional-list">
        <li v-for="(item,index) in aiAnalysis.otherHealthIndicators" :key="index" class="additional-item">
          {{ item }}
        </li>
      </ul>
    </view>

    <view class="button-container">
      <button class="update-report-button" @click="onClickRefresh(userObjectId || userInfo.userObjectId)">更新报告</button>
      <button class="back-button" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
import groupx from '../../api/api-groupx';
import myStorage from '../../common/my-storage';
export default {
  name: "HealthCheckResult",
  data() {
    return {
      userObjectId: myStorage.getUserObjectId(),
      animationData: {},
      userInfo: myStorage.getUserInfo(),
      aiSummary: undefined,
      showRefreshToast: false
    };
  },
  computed: {
    aiAnalysis() {
      return this.aiSummary;
    }
  },
  onReady() {

    this.initData();
  },
  methods: {
    initData() {
      this.userInfo = myStorage.getUserInfo();
      this.userObjectId = myStorage.getUserObjectId();
      this.aiSummary = undefined;
      console.log("初始化数据", this.userObjectId, this.userInfo);

      const userOID = this.userObjectId || this.userInfo.userObjectId
      this.refreshReport(userOID);
    },
    onClickRefresh(userOID) {
      this.showRefreshToast = true
      this.refreshReport(userOID);
    },
    refreshReport(userObjectId) {
      console.log("刷新体检报告：", userObjectId, this.userInfo)
      if (!this.userInfo?.nickName) {
        uni.showToast({
          title: '请返回并进入“我的信息”页面设置昵称',
          icon: 'none',
          duration: 6000
        })

        this.aiSummary = undefined
        return
      }

      console.log("刷新体检报告:", userObjectId, this.showRefreshToast)
      // 从服务器获取最新的体检报告
      groupx.getHealthReport(userObjectId).then(res => {
        console.log('最新体检报告:', res, this.showRefreshToast)
        if (res.code == 200) {
          this.aiSummary = res.data?.aiSummary;
          console.log("更新体检报告成功", this.aiSummary);
          // 更新报告数据逻辑
          this.showRefreshToast && uni.showToast({ title: '更新体检报告成功', icon: 'success' });
          this.showRefreshToast = false
        } else {
          this.aiSummary = null;
          this.showRefreshToast && uni.showToast({ title: res.msg || '获取报告失败', icon: 'error' });
          this.showRefreshToast = false
        }
      }).catch(err => {
        console.error('获取报告失败:', err);
        this.aiSummary = null;
        this.showRefreshToast && uni.showToast({ title: '获取报告失败', icon: 'error' });
        this.showRefreshToast = false
      });
    },

    goBack() {
      uni.navigateTo({
        url: '/pages/health_check/index',
        success: () => {
          console.log('返回到健康检查首页');
        },
        fail: (err) => {
          console.error('返回失败:', err);
        }
      });
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/common-form.scss';
@import '@/styles/variables.scss';

.mine-container {
  background: linear-gradient(135deg, #1a73e8, #29b6f6);
  min-height: 100vh;
  padding-bottom: 0;
  color: #fff;
}

.top-space {
  height: 80upx;
}

.header {
  width: 100%;
  padding: 70rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
  letter-spacing: 4rpx;
}

.result-container {
  margin-top: 20px;
  text-align: center;
}

.result-text {
  margin-top: 20px;
}

.result-description {
  font-size: 32upx;
  color: rgba(255, 255, 255, 0.85);
  text-align: left;
  margin-bottom: 30rpx;
}

/* AI 分析模块样式 */
.ai-analysis-section {
  margin: 0 16upx 12upx 16upx;
}

.analysis-card {
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16upx;
  padding: 24upx;
  box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
}

.analysis-header {
  margin-bottom: 16upx;
}

.analysis-title {
  font-size: 32upx;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.analysis-content {
  font-size: 28upx;
  color: rgba(255, 255, 255, 0.85);
  line-height: 1.5;
}

.recommendation-title,
.additional-title {
  display: block;
  margin-top: 16upx;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.9);
  font-size: 28upx;
}

.recommendation-list,
.additional-list {
  list-style-type: disc;
  padding-left: 40upx;
  margin-top: 8upx;
  font-size: 28upx;
}

.recommendation-item,
.additional-item {
  margin-top: 8upx;
  color: rgba(255, 255, 255, 0.85);
  font-size: 28upx;
}

.additional-info {
  margin: 0 16upx 12upx 16upx;
  background-color: rgba(255, 255, 255, 0.15);
  border-radius: 16upx;
  padding: 24upx;
  box-shadow: 0 4upx 16upx rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.25);
}

.button-container {
  display: flex;
  justify-content: space-between;
  margin: 40upx 16upx;
  gap: 20upx;
}

.update-report-button,
.back-button {
  flex: 1;
  height: 88upx;
  line-height: 88upx;
  font-size: 32upx;
  border-radius: 44upx;
  color: #fff;
  text-align: center;
  border: none;
}

.update-report-button {
  background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
  box-shadow: 0 4upx 12upx rgba(59, 126, 248, 0.3);
}

.back-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
</style>