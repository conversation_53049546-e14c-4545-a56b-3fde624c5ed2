<template>
	<view class="form-container">
		<image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
		<view class="title">健康科学减重</view>
		<view class="step-indicator">
			<view class="step active">1</view>
			<view class="step-line"></view>
			<view class="step">2</view>
			<view class="step-line"></view>
			<view class="step">3</view>
		</view>
		<view class="sub-title">请设置您的体重管理目标</view>

		<view class="content-section">
			<view class="section-title">体重管理目标</view>

			<view class="input-group">
				<view class="input-item">
					<text class="input-label">当前体重 (kg)</text>
					<view class="weight-input-group">
						<button class="weight-btn" @click="adjustWeight('initial', -1)">-</button>
						<input type="digit" v-model="initialWeight" class="weight-input"
							@input="validateWeightInputs" />
						<button class="weight-btn" @click="adjustWeight('initial', 1)">+</button>
					</view>
				</view>

				<view class="input-item">
					<text class="input-label">目标体重 (kg)</text>
					<view class="weight-input-group">
						<button class="weight-btn" @click="adjustWeight('target', -1)">-</button>
						<input type="digit" v-model="targetWeight" class="weight-input" @input="validateWeightInputs" />
						<button class="weight-btn" @click="adjustWeight('target', 1)">+</button>
					</view>
				</view>
			</view>

			<view class="section-title">减重周期</view>
			<view class="radio-group-horizontal">
				<view v-for="(period, index) in weightLossPeriods" :key="index"
					:class="['radio-item-horizontal', selectedPeriod === period.value ? 'radio-selected' : '']"
					@click="selectPeriod(period.value)">
					<view class="radio-circle">
						<view v-if="selectedPeriod === period.value" class="radio-inner"></view>
					</view>
					<text class="radio-label">{{ period.label }}</text>
				</view>
			</view>

			<view class="section-title">空腹血糖值 (mmol/L)</view>
			<view class="input-item full-width">
				<input type="digit" v-model="bloodSugar" class="blood-sugar-input" @input="validateBloodSugar" />
				<text class="input-hint">正常范围: 3.9-6.1 mmol/L</text>
			</view>

			<view class="section-title">血压水平 (mmHg)</view>
			<view class="input-item full-width">
				<input type="digit" v-model="bloodPressure" class="blood-sugar-input" @input="validateBloodPressure" />
				<text class="input-hint">正常范围: 90-140 mmHg</text>
			</view>

			<view class="section-title">胆固醇水平 (mmol/L)</view>
			<view class="input-item full-width">
				<input type="digit" v-model="cholesterol" class="blood-sugar-input" @input="validateCholesterol" />
				<text class="input-hint">正常范围: 2.8-5.17 mmol/L</text>
			</view>
		</view>
		<view class="footer">
			<view class="tips-text">填写体重目标,获得个性化减重计划</view>
			<button :class="[weightInputValid ? 'next-button' : 'next-button-disabled']"
				@click="goToBodyCondition">继续(1/3)</button>
		</view>
	</view>
</template>

<script>
import groupx from "@/api/api-groupx"
import myStorage from "../../common/my-storage"
import { userInfo } from "os"
export default {
	data() {
		return {
			title: '健康科学减重',
			submitBtnText: '下一步',
			myLongFor: false,
			bg_img: require('@/static/640.png'),
			nextDisabled: true,
			newTag: '',
			tags_sel: { '1': [], '2': [], '3': [], '4': [], '5': [] },
			tags: { 'sport': [], 'art': [], 'life': [], 'other': [] },
			curTags: [{ text: "露营", sel: false }, { text: "跑步", sel: false }, { text: "骑行", sel: false }, { text: "网球", sel: false }, { text: "有氧", sel: false }],
			curTagsType: '',
			moreDlgVisible: false,
			addTagDlgVisible: false,
			tags_1: ['良好', '一般', '欠佳'],
			tags_2: ['充足', '一般', '不足'],
			tags_3: ['正常', '较大', '很大'],
			tags_4: ['稳定', '波动', '增加'],
			tags_5: ['良好', '一般', '紧张'],
			scrollIntoViewId: '',
			userInfo: myStorage.getUserInfo(),

			// 新增字段
			initialWeight: '',
			targetWeight: '',
			selectedPeriod: '1',
			weightLossPeriods: [
				{ label: '1个月', value: '1' },
				{ label: '3个月', value: '3' },
				{ label: '6个月', value: '6' }
			],
			bloodSugar: '',
			healthCondition: '',
			bloodPressure: '',
			cholesterol: '',

			// 表单验证
			weightInputValid: false,
			bloodSugarValid: true,
			bloodPressureValid: true,
			cholesterolValid: true
		}
	},
	onReady() {
		console.log("tag-loadHealthSurveyData onReady")
		this.loadHealthSurveyData()
		
	},
	methods: {

		// 计算选择的标签数量
		countSelTags() {
			let totalElements = 0;
			for (const key in this.tags_sel) {
				if (Array.isArray(this.tags_sel[key])) {
					totalElements += this.tags_sel[key].length;
				}
			}
			return totalElements;
		},
		// 遍历每个标签，并将其对应的数组合并到结果数组中
		getSelTagsString() {
			let totalElements = [];
			for (const key in this.tags_sel) {
				const sel = this.tags_sel[key]
				if (Array.isArray(sel)) {
					sel.forEach(index => {
						totalElements.push(this.tags[key][index]);
					});
				}
			}
			console.log("totalElements:", totalElements)
			return totalElements;
		},
		// 调整体重值
		adjustWeight(type, amount) {
			if (type === 'initial') {
				let weight = parseFloat(this.initialWeight) || 0;
				weight = Math.max(0, weight + amount);
				this.initialWeight = weight.toString();
			} else if (type === 'target') {
				let weight = parseFloat(this.targetWeight) || 0;
				weight = Math.max(0, weight + amount);
				this.targetWeight = weight.toString();
			}
			this.validateWeightInputs();
		},

		// 验证体重输入
		validateWeightInputs() {
			const initialW = parseFloat(this.initialWeight);
			const targetW = parseFloat(this.targetWeight);

			if (isNaN(initialW) || isNaN(targetW) || initialW <= 0 || targetW <= 0) {
				this.weightInputValid = false;
			} else if (targetW >= initialW) {
				uni.showToast({
					title: '目标体重应小于当前体重',
					icon: 'none'
				});
				this.weightInputValid = false;
			} else {
				this.weightInputValid = true;
			}
		},

		// 验证血糖值
		validateBloodSugar() {
			const sugar = parseFloat(this.bloodSugar);

			if (this.bloodSugar === '') {
				// 允许为空
				this.bloodSugarValid = true;
			} else if (isNaN(sugar) || sugar < 0) {
				this.bloodSugarValid = false;
			} else {
				this.bloodSugarValid = true;
			}
		},

		// 验证血压值
		validateBloodPressure() {
			const pressure = parseFloat(this.bloodPressure);
			if (this.bloodPressure === '') {
				this.bloodPressureValid = true;
			} else if (isNaN(pressure) || pressure < 0) {
				this.bloodPressureValid = false;
			} else {
				this.bloodPressureValid = true;
			}
		},

		// 验证胆固醇值
		validateCholesterol() {
			const chol = parseFloat(this.cholesterol);
			if (this.cholesterol === '') {
				this.cholesterolValid = true;
			} else if (isNaN(chol) || chol < 0) {
				this.cholesterolValid = false;
			} else {
				this.cholesterolValid = true;
			}
		},

		// 选择减重周期
		selectPeriod(value) {
			this.selectedPeriod = value;
		},

		// 前往身体状况评估页面
		goToBodyCondition() {
			console.log("goToBodyCondition");

			// 验证必填项
			if (!this.initialWeight || !this.targetWeight) {
				uni.showToast({
					title: '请填写当前体重和目标体重',
					icon: 'none'
				});
				return;
			}

			if (this.weightInputValid && this.bloodSugarValid && this.bloodPressureValid && this.cholesterolValid) {
				// 保存用户选择的体重管理数据
				let healthData = {};

				// 保存体重相关数据
				healthData.weightManagement = {
					initialWeight: parseFloat(this.initialWeight),
					targetWeight: parseFloat(this.targetWeight),
					weightLossPeriod: parseInt(this.selectedPeriod)
				};

				// 保存血糖值
				if (this.bloodSugar) {
					healthData.weightManagement.bloodSugar = parseFloat(this.bloodSugar);
				}

				// 保存血压值
				if (this.bloodPressure) {
					healthData.weightManagement.bloodPressure = parseFloat(this.bloodPressure);
				}

				// 保存胆固醇值
				if (this.cholesterol) {
					healthData.weightManagement.cholesterol = parseFloat(this.cholesterol);
				}

				// 将数据存储到本地
				let user = myStorage.getUserInfo();
				if (!user.healthData) {
					user.healthData = {};
				}

				// 合并数据
				user.healthData = { ...user.healthData, ...healthData };
				myStorage.setUserInfo(user);

				// 跳转到身体状况评估页面
				uni.navigateTo({ url: '/pages/body-condition/body-condition' });
			} else {
				uni.showToast({
					title: '请检查输入是否正确',
					icon: 'none'
				});
			}
		},

		loadHealthSurveyData() {
			// 检查是否已有保存的数据
			const user = myStorage.getUserInfo();

			if (user && user.healthData) {
				// 恢复体重管理数据
				if (user.healthData.weightManagement) {
					const weightData = user.healthData.weightManagement;
					this.initialWeight = weightData.initialWeight.toString();
					this.targetWeight = weightData.targetWeight.toString();
					this.selectedPeriod = weightData.weightLossPeriod.toString();

					// 恢复血压值
					if (weightData.bloodPressure) {
						this.bloodPressure = weightData.bloodPressure.toString();
						this.validateBloodPressure();
					}

					// 恢复胆固醇值
					if (weightData.cholesterol) {
						this.cholesterol = weightData.cholesterol.toString();
						this.validateCholesterol();
					}

					this.validateWeightInputs();
				}

				// 恢复血糖值
				if (user.healthData?.weightManagement?.bloodSugar) {
					this.bloodSugar = user.healthData.weightManagement.bloodSugar.toString();
					this.validateBloodSugar();
				}
				return;
			}

			groupx.getHealthSurvey().then(res => {
				console.log("getHealthSurvey:", res)
				if (res.code === 200) {
					const surveyData = res.data;
					this.userInfo.healthData = surveyData;
					
					// 处理体重管理数据
					if (surveyData.weightManagement) {
						this.userInfo.healthData.weightManagement = surveyData.weightManagement;
						const weightData = surveyData.weightManagement;
						
						// 设置体重相关数据
						this.initialWeight = weightData.initialWeight.toString();
						this.targetWeight = weightData.targetWeight.toString();
						this.selectedPeriod = weightData.weightLossPeriod.toString();
						
						// 设置血糖值
						if (weightData?.bloodSugar) {
							this.bloodSugar = weightData.bloodSugar.toString();
						}
						
						// 设置血压值
						if (weightData?.bloodPressure) {
							this.bloodPressure = weightData.bloodPressure.toString();
						}
						
						// 设置胆固醇值
						if (weightData?.cholesterol) {
							this.cholesterol = weightData.cholesterol.toString();
						}
						
						this.validateWeightInputs();
						this.validateBloodSugar();
						this.validateBloodPressure();
						this.validateCholesterol();
					}
					
					// 处理身体状况数据
					if (surveyData.bodyCondition) {
						this.userInfo.healthData.bodyCondition = surveyData.bodyCondition;
					}
					
					
					
					// 保存更新后的用户信息
					myStorage.setUserInfo(this.userInfo);
				}
			})
		}
	}
}
</script>



<style lang="scss" scoped>
@import '@/styles/common-form.scss';



// 自定义样式

// 体重输入组
.weight-input-group {
	display: flex;
	align-items: center;
	width: 100%;
}

.weight-btn {
	width: 60rpx;
	height: 60rpx;
	line-height: 56rpx;
	background-color: rgba(255, 255, 255, 0.3);
	color: $text-primary;
	border-radius: 30rpx;
	font-size: 32rpx;
	font-weight: bold;
	padding: 0;
	margin: 0;
	display: flex;
	justify-content: center;
	align-items: center;
}

.weight-input {
	flex: 1;
	height: 60rpx;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 8rpx;
	padding: 0 10rpx;
	margin: 0 8rpx;
	color: $text-primary;
	text-align: center;
	font-size: 28rpx;
	min-width: 0;
	/* 防止输入框溢出 */

	&::placeholder {
		color: rgba(255, 255, 0, 0.6);
		/* 黄色提示文字 */
	}
}

.blood-sugar-input {
	width: 100%;
	height: 60rpx;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 8rpx;
	padding: 0 20rpx;
	color: $text-primary;
	text-align: left;
	font-size: 28rpx;
	box-sizing: border-box;

	&::placeholder {
		color: rgba(255, 255, 0, 0.6);
		/* 黄色提示文字 */
	}
}

.input-group {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
}

// 单选按钮组
.radio-group-horizontal {
	display: flex;
	justify-content: space-between;
	margin-bottom: 20rpx;
	width: 100%;
}

.radio-item-horizontal {
	display: flex;
	align-items: center;
	flex: 1;
	margin: 0 5rpx;
	padding: 12rpx 8rpx;
	background-color: rgba(255, 255, 255, 0.1);
	border-radius: 8rpx;

	&:first-child {
		margin-left: 0;
	}

	&:last-child {
		margin-right: 0;
	}

	&.radio-selected {
		background-color: rgba(255, 255, 255, 0.3);
	}
}

.radio-circle {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	border: 2rpx solid $text-primary;
	margin-right: 8rpx;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-shrink: 0;

	.radio-inner {
		width: 18rpx;
		height: 18rpx;
		border-radius: 50%;
		background-color: $text-primary;
	}
}

.radio-label {
	font-size: 26rpx;
	color: $text-primary;
	white-space: nowrap;
}












.edit-text {
	margin-top: 20rpx;
	font-size: 28rpx;
	color: #4D8CFF;
	text-decoration: underline;
	cursor: pointer;
}

// 对话框样式
.dialog-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-color: #ffffff;
	display: flex;
	justify-content: center;
	align-items: center;
}

.dialog-more-container {
	padding: 20rpx;

	.dialog-content-more {
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-items: center;

		.title {
			font-family: PingFangSC-Regular;
			font-size: 36rpx;
			color: #000000;
			text-align: center;
			line-height: 50rpx;
		}

		.dlg-button-sel {
			margin: 10rpx;
			line-height: 70rpx;
			font-size: medium;
			text-align: center;
			color: #000000;
			background-color: #ffffff;
			border: none;
			border-radius: 10rpx;
			display: inline-block;
		}

		.dlg-button {
			margin: 10rpx;
			line-height: 70rpx;
			font-size: medium;
			text-align: center;
			color: #D5E4FF;
			background-color: #ffffff10;
			border: none;
			border-radius: 10rpx;
			display: inline-block;
		}
	}

	.dialog-footer {
		width: 100%;
		display: flex;
		justify-content: space-between;

		.dialog-button {
			margin-top: 20rpx;
			background-color: #ffffff00;
			width: 100%;
			border-radius: 16rpx;
			font-size: medium;
			padding: 6rpx;
			font-size: 32rpx;

			&:nth-child(1) {
				color: #D5E4FF;
				border: 1px solid #ffffffff;
				margin-right: 10rpx;
			}

			&:nth-child(2) {
				color: white;
				margin-left: 10rpx;
				background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
				box-shadow: 0rpx 25rpx 50rpx #74a5ff6f;
			}

			&:nth-child(1):active,
			&:nth-child(2):active {
				background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
			}

			&:nth-child(1):active {
				color: #fff;
			}
		}
	}
}

.dialog-button-sel {
	margin: 10rpx;
	color: black;
	height: 80rpx;
	line-height: 80rpx;
	background: white;
	padding: 2upx 40rpx;
	border: none;
	border-radius: 12rpx;
	font-size: 32rpx;
	cursor: pointer;
}

.dialog-content {
	width: 620rpx;
	padding: 30upx 38upx 30upx 33rpx;
	border-radius: 21rpx;
	display: flex;
	flex-direction: row;
	flex-wrap: wrap;
	align-items: center;

	.title {
		font-family: PingFangSC-Regular;
		font-size: 36rpx;
		color: #000000;
		text-align: center;
		line-height: 50rpx;
	}

	.dialog-button {
		margin: 10rpx;
		color: white;
		line-height: 80rpx;
		background: rgba(255, 255, 255, 0.12);
		padding: 2upx 40rpx;
		border: none;
		border-radius: 12rpx;
		font-size: 32rpx;
		cursor: pointer;
	}
}
</style>