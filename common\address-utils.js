import myStorage from './my-storage'

export const fetchAddressDataFromTencent = () => {
    // 如果服务器获取失败,使用本地数据
    const defaultData = [{
        label: '浙江', children: [{ label: '杭州', children: [{ label: '上城' }] }]
    }, {
        label: '四川', children: [{ label: '成都', children: [{ label: '锦里' }] }]
    }, {
        label: '江苏', children: [{ label: '南京', children: [{ label: '夫子庙' }] },
        { label: '苏州', children: [{ label: '相城' }] },
        { label: '无锡', children: [{ label: '惠山' }] },
        { label: '常州', children: [{ label: '钟楼' }, { label: '天宁' }, { label: '青羊' }] },
        { label: '扬州', children: [{ label: '广陵' }] },
        { label: '泰州', children: [{ label: '海陵' }] },
        { label: '宿迁', children: [{ label: '宿城' }] },
        ]
    }, {
        label: '广东', children: [{ label: '广州', children: [{ label: '黄浦江' }] }]
    }, {
        label: '山东', children: [{ label: '青岛', children: [{ label: '崂山' }] }]
    }, {
        label: '河南', children: [{ label: '郑州', children: [{ label: '金水' }] }]
    }, {
        label: '湖北', children: [{ label: '武汉', children: [{ label: '洪山' }] }]
    }, {
        label: '河北', children: [{ label: '石家庄', children: [{ label: '新华' }] }]
    }, {
        label: '山西', children: [{ label: '太原', children: [{ label: '迎泽' }] }]
    }]

    return new Promise((resolve) => {
        uni.request({
            url: 'https://apis.map.qq.com/ws/district/v1/list?key=JXMBZ-3AG6C-BNX2W-AVNAH-LANLF-UGFYP',
            method: 'GET',
            success: (res) => {
                if (res.statusCode === 200 && res.data) {
                    const result = myStorage.setRegionData(res.data)
                    resolve(result)
                } else {
                    resolve(defaultData)
                }
            },
            fail: () => {
                resolve(defaultData)
            }
        })
    })
} 