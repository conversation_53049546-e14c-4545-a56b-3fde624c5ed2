{"name": "渐随未济健康WXApp", "version": "1.6.0", "description": "", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@qiun/ucharts": "2.5.0-20230101", "@types/moment": "^2.13.0", "crypto-js": "^4.2.0", "dotenv": "^16.4.5", "moment": "^2.30.1", "spark-md5": "^3.0.2", "typescript": "^5.4.2", "vuex-persistedstate": "^2.7.0", "webpack": "^5.91.0"}, "devDependencies": {"@types/node": "^20.11.27", "@types/uni-app": "^1.4.8"}}