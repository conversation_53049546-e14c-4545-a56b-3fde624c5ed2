<template>
	<view class="container">
		<view class="header">
			<view class="user-info">
				<image class="avatar" src="/static/icons/user-default.png"></image>
				<text class="username">{{ userInfo.username }}</text>
			</view>
			<view class="date">{{ currentDate }}</view>
		</view>

		<view class="summary-card">
			<view class="summary-title">本月总结</view>
			<view class="summary-table">
				<view class="table-row">
					<view class="table-cell header-cell">日期</view>
					<view class="table-cell" v-for="(date, index) in summaryData.dates" :key="index">{{ date }}</view>
				</view>
				<view class="table-row">
					<view class="table-cell header-cell">体重</view>
					<view class="table-cell" v-for="(weight, index) in summaryData.weights" :key="index">{{ weight }}KG</view>
				</view>
				<view class="table-row">
					<view class="table-cell header-cell">目标</view>
					<view class="table-cell" v-for="(target, index) in summaryData.targets" :key="index">{{ target }}</view>
					<view class="table-cell target-cell">干预目标: {{ summaryData.interventionTarget }}KG</view>
				</view>
			</view>
			<view class="summary-stats">
				<text>已坚持饮食 <text class="highlight">{{ summaryData.dietDays }}</text> 天</text>
				<text>运动了 <text class="highlight">{{ summaryData.exerciseDays }}</text> 天</text>
			</view>
		</view>

		<view class="punch-card-section">
			<view class="punch-card-header">
				<view class="punch-card-title">打卡记录</view>
				<view class="time-tags">
					<view
						v-for="(item, index) in timeRanges"
						:key="index"
						class="time-tag"
						:class="{ active: selectedTimeRange === item.value }"
						@click="selectTimeRange(item.value)">
						{{ item.label }}
					</view>
				</view>
			</view>
			<view class="calendar-grid">
				<view class="calendar-row" v-for="(week, weekIndex) in calendarWeeks" :key="weekIndex">
					<view
						class="calendar-day"
						v-for="(day, dayIndex) in week"
						:key="dayIndex"
						:class="{ 'has-record': day.hasRecord, 'today': day.isToday }">
						<text class="day-text">{{ day.date }}</text>
						<view v-if="day.hasRecord" class="punch-dot"></view>
					</view>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">体重变化曲线(KG)</view>
			<view class="chart-container">
				<canvas
					canvas-id="weightChart"
					id="weightChart"
					class="chart-canvas"
					:style="{ width: chartWidth + 'px', height: chartHeight + 'px' }">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color actual-weight"></view>
					<text class="legend-text">实际体重</text>
				</view>
				<view class="legend-item">
					<view class="legend-color target-weight"></view>
					<text class="legend-text">目标体重</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">睡眠时长(H)</view>
			<image class="chart-image" src="/static/images/sleep-chart.png"></image>
		</view>

		<view class="chart-section">
			<view class="chart-title">饮水(ML)</view>
			<image class="chart-image" src="/static/images/water-chart.png"></image>
		</view>

		<view class="chart-section">
			<view class="chart-title">运动时长(小时)</view>
			<image class="chart-image" src="/static/images/exercise-chart.png"></image>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				username: 'yinghk'
			},
			currentDate: '2025-06-09',
			summaryData: {
				dates: ['05/10', '05/17', '05/24', '06/09'],
				weights: [0, 0, 65, 65],
				targets: ['第一个月 (5%)64.60KG', '第二个月 (5%)61.20KG', '第三个月 (5%)57.80KG'],
				interventionTarget: 57.80,
				dietDays: 4,
				exerciseDays: 4
			},
			timeRanges: [
				{ label: '一周', value: 'week' },
				{ label: '两周', value: 'twoWeeks' },
				{ label: '一个月', value: 'month' }
			],
			selectedTimeRange: 'week',
			punchCardData: {
				'week': {
					days: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, true, true, true, true],
					exercise: [false, false, false, true, true, true, true]
				},
				'twoWeeks': {
					days: ['05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, false, false, false, false, false, false, false, true, true, true, true],
					exercise: [false, false, false, false, false, false, false, false, false, false, true, true, true, true]
				},
				'month': {
					days: ['05/10', '05/11', '05/12', '05/13', '05/14', '05/15', '05/16', '05/17', '05/18', '05/19', '05/20', '05/21', '05/22', '05/23', '05/24', '05/25', '05/26', '05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: Array(26).fill(false).concat([true, true, true, true]),
					exercise: Array(26).fill(false).concat([true, true, true, true])
				}
			},
			currentPeriod: 'week', // 'week', 'twoWeeks', 'month'
			chartWidth: 350,
			chartHeight: 250,
			weightChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					actualWeights: [65.8, 65.6, 65.4, 65.2, 65.1, 65.0, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					actualWeights: [66.5, 66.2, 66.0, 65.8, 65.4, 65.2, 65.1, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					actualWeights: [68.0, 67.2, 66.8, 66.0, 65.2, 65.0],
					targetWeights: [64.6, 61.2, 57.8, 57.8, 57.8, 57.8]
				}
			}
		}
	},
	computed: {
		currentWeekDays() {
			return this.punchCardData[this.selectedTimeRange].days;
		},
		combinedPunchCard() {
			const diet = this.punchCardData[this.selectedTimeRange].diet;
			const exercise = this.punchCardData[this.selectedTimeRange].exercise;
			return diet.map((d, i) => d || exercise[i]);
		},
		calendarWeeks() {
			const days = this.currentWeekDays;
			const punchData = this.combinedPunchCard;
			const today = this.currentDate.replace(/-/g, '/').substring(5); // 转换为 MM/DD 格式

			// 将日期数据转换为日历格式
			const calendarDays = days.map((day, index) => ({
				date: day,
				hasRecord: punchData[index],
				isToday: day === today
			}));

			// 按周分组（每7天一行）
			const weeks = [];
			for (let i = 0; i < calendarDays.length; i += 7) {
				weeks.push(calendarDays.slice(i, i + 7));
			}

			return weeks;
		}
	},
	onLoad() {
		// Load data when the page loads
	},
	onReady() {
		this.$nextTick(() => {
			this.initWeightChart();
		});
	},
	methods: {
		selectTimeRange(range) {
			this.selectedTimeRange = range;
			// 更新图表数据
			this.updateWeightChart();
		},
		bindPickerChange(e) {
			this.pickerIndex = e.detail.value;
			if (this.pickerIndex == 0) {
				this.selectedTimeRange = 'week';
			} else if (this.pickerIndex == 1) {
				this.selectedTimeRange = 'twoWeeks';
			} else if (this.pickerIndex == 2) {
				this.selectedTimeRange = 'month';
			}
		},
		initWeightChart() {
			const ctx = uni.createCanvasContext('weightChart', this);
			this.drawChart(ctx);
		},
		updateWeightChart() {
			const ctx = uni.createCanvasContext('weightChart', this);
			this.drawChart(ctx);
		},
		drawChart(ctx) {
			const data = this.weightChartData[this.selectedTimeRange];
			const { dates, actualWeights, targetWeights } = data;

			// 清空画布
			ctx.clearRect(0, 0, this.chartWidth, this.chartHeight);

			// 设置画布背景
			ctx.fillStyle = '#ffffff';
			ctx.fillRect(0, 0, this.chartWidth, this.chartHeight);

			// 计算绘图区域
			const padding = 40;
			const chartArea = {
				left: padding,
				top: padding,
				right: this.chartWidth - padding,
				bottom: this.chartHeight - padding,
				width: this.chartWidth - 2 * padding,
				height: this.chartHeight - 2 * padding
			};

			// 计算数据范围
			const allWeights = [...actualWeights, ...targetWeights];
			const minWeight = Math.min(...allWeights) - 1;
			const maxWeight = Math.max(...allWeights) + 1;
			const weightRange = maxWeight - minWeight;

			// 绘制网格线
			this.drawGrid(ctx, chartArea, dates.length, minWeight, maxWeight);

			// 绘制坐标轴
			this.drawAxes(ctx, chartArea, dates, minWeight, maxWeight);

			// 绘制折线
			this.drawLine(ctx, chartArea, actualWeights, minWeight, weightRange, '#007bff', 2);
			this.drawLine(ctx, chartArea, targetWeights, minWeight, weightRange, '#28a745', 2);

			// 绘制数据点
			this.drawPoints(ctx, chartArea, actualWeights, minWeight, weightRange, '#007bff');
			this.drawPoints(ctx, chartArea, targetWeights, minWeight, weightRange, '#28a745');

			ctx.draw();
		},
		drawGrid(ctx, area, dataCount, minWeight, maxWeight) {
			ctx.strokeStyle = '#f0f0f0';
			ctx.lineWidth = 1;

			// 垂直网格线
			for (let i = 0; i <= dataCount - 1; i++) {
				const x = area.left + (i * area.width) / (dataCount - 1);
				ctx.beginPath();
				ctx.moveTo(x, area.top);
				ctx.lineTo(x, area.bottom);
				ctx.stroke();
			}

			// 水平网格线
			const gridLines = 5;
			for (let i = 0; i <= gridLines; i++) {
				const y = area.bottom - (i * area.height) / gridLines;
				ctx.beginPath();
				ctx.moveTo(area.left, y);
				ctx.lineTo(area.right, y);
				ctx.stroke();
			}
		},
		drawAxes(ctx, area, dates, minWeight, maxWeight) {
			ctx.strokeStyle = '#333';
			ctx.lineWidth = 2;
			ctx.font = '12px Arial';
			ctx.fillStyle = '#666';

			// X轴
			ctx.beginPath();
			ctx.moveTo(area.left, area.bottom);
			ctx.lineTo(area.right, area.bottom);
			ctx.stroke();

			// Y轴
			ctx.beginPath();
			ctx.moveTo(area.left, area.top);
			ctx.lineTo(area.left, area.bottom);
			ctx.stroke();

			// X轴标签（日期）
			dates.forEach((date, index) => {
				const x = area.left + (index * area.width) / (dates.length - 1);
				ctx.fillText(date, x - 15, area.bottom + 20);
			});

			// Y轴标签（体重）
			const gridLines = 5;
			for (let i = 0; i <= gridLines; i++) {
				const weight = minWeight + (i * (maxWeight - minWeight)) / gridLines;
				const y = area.bottom - (i * area.height) / gridLines;
				ctx.fillText(weight.toFixed(1), area.left - 35, y + 4);
			}
		},
		drawLine(ctx, area, weights, minWeight, weightRange, color, lineWidth) {
			ctx.strokeStyle = color;
			ctx.lineWidth = lineWidth;
			ctx.beginPath();

			weights.forEach((weight, index) => {
				const x = area.left + (index * area.width) / (weights.length - 1);
				const y = area.bottom - ((weight - minWeight) / weightRange) * area.height;

				if (index === 0) {
					ctx.moveTo(x, y);
				} else {
					ctx.lineTo(x, y);
				}
			});

			ctx.stroke();
		},
		drawPoints(ctx, area, weights, minWeight, weightRange, color) {
			ctx.fillStyle = color;

			weights.forEach((weight, index) => {
				const x = area.left + (index * area.width) / (weights.length - 1);
				const y = area.bottom - ((weight - minWeight) / weightRange) * area.height;

				ctx.beginPath();
				ctx.arc(x, y, 4, 0, 2 * Math.PI);
				ctx.fill();
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20upx;
	background-color: #f0f5f9; /* 浅蓝色调背景 */
	min-height: 100vh;
	font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'microsoft yahei', sans-serif;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30upx;
	padding: 20upx 0;

	.user-info {
		display: flex;
		align-items: center;

		.avatar {
			width: 80upx;
			height: 80upx;
			border-radius: 50%;
			margin-right: 15upx;
			border: 2upx solid #fff; /* 头像边框 */
		}

		.username {
			font-size: 36upx;
			font-weight: bold;
			color: #333;
		}
	}

	.date {
		font-size: 28upx;
		color: #666;
	}
}

.summary-card,
.punch-card-section,
.chart-section {
	background-color: #fff;
	border-radius: 20upx; /* 更圆润的边角 */
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 24upx rgba(0, 0, 0, 0.08); /* 更明显的阴影 */
}

.summary-title,
.punch-card-title,
.chart-title {
	font-size: 36upx; /* 标题字号增大 */
	font-weight: bold;
	margin-bottom: 25upx; /* 标题下边距增大 */
	color: #333;
	text-align: center; /* 标题居中 */
}

.summary-table {
	display: table;
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20upx;

	.table-row {
		display: table-row;

		.table-cell {
			display: table-cell;
			padding: 18upx 10upx; /* 单元格内边距增大 */
			border: 1upx solid #e0e0e0; /* 边框颜色变浅 */
			text-align: center;
			font-size: 28upx; /* 字体增大 */
			color: #555;

			&.header-cell {
				background-color: #eaf4f9; /* 表头背景色 */
				font-weight: bold;
				color: #333;
			}

			&.target-cell {
				background-color: #ffebeb; /* 目标背景色 */
				color: #e40000;
				font-weight: bold;
			}
		}
	}
}

.summary-stats {
	display: flex;
	justify-content: space-around;
	font-size: 30upx; /* 字体增大 */
	color: #666;
	padding-top: 15upx; /* 增加上边距 */

	.highlight {
		color: #007bff; /* 蓝色高亮 */
		font-weight: bold;
		margin: 0 8upx; /* 间距增大 */
	}
}

.punch-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25upx;

	.punch-card-title {
		margin-bottom: 0;
		text-align: left;
	}

	.time-tags {
		display: flex;
		gap: 15upx;

		.time-tag {
			padding: 8upx 20upx;
			border-radius: 20upx;
			font-size: 26upx;
			color: #666;
			background-color: #f5f5f5;
			border: 1upx solid #e0e0e0;
			transition: all 0.3s ease;
			cursor: pointer;

			&.active {
				background-color: #007bff;
				color: white;
				border-color: #007bff;
			}

			&:hover {
				background-color: #e6f3ff;
			}
		}
	}
}

.calendar-grid {
	display: flex;
	flex-direction: column;
	gap: 10upx;

	.calendar-row {
		display: flex;
		justify-content: space-around;
		gap: 8upx;

		.calendar-day {
			flex: 1;
			min-height: 80upx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #f8f9fa;
			border-radius: 12upx;
			border: 1upx solid #e0e0e0;
			position: relative;
			transition: all 0.3s ease;

			&.has-record {
				background-color: #e8f5e8;
				border-color: #4CAF50;
			}

			&.today {
				background-color: #e6f3ff;
				border-color: #007bff;
				border-width: 2upx;
			}

			&.has-record.today {
				background-color: #d4edda;
				border-color: #28a745;
			}

			.day-text {
				font-size: 24upx;
				color: #333;
				font-weight: 500;
			}

			.punch-dot {
				width: 12upx;
				height: 12upx;
				background-color: #4CAF50;
				border-radius: 50%;
				margin-top: 4upx;
			}
		}
	}
}

.chart-section {
	text-align: center;

	.chart-title {
		color: #333;
	}

	.chart-container {
		width: 100%;
		height: 450upx;
		display: flex;
		justify-content: center;
		align-items: center;

		.chart-canvas {
			width: 100%;
			height: 100%;
		}
	}

	.chart-image {
		width: 100%;
		height: 450upx; /* 图表高度增加 */
		object-fit: contain;
	}
}
</style>
