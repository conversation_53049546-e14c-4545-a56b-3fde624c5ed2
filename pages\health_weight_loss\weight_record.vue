<template>
	<view class="container">
		<view class="header">
			<view class="user-info">
				<image class="avatar" src="/static/icons/user-default.png"></image>
				<text class="username">{{ userInfo.username }}</text>
			</view>
			<view class="date">{{ currentDate }}</view>
		</view>

		<view class="summary-card">
			<view class="summary-title">{{ summaryTitle }}</view>
			<view class="summary-content">
				<view class="summary-row">
					<view class="summary-item" @click="editStartDate">
						<view class="item-label">起始日期</view>
						<view class="item-value" :class="{ 'undefined-value': !summaryData.startDate }">
							{{ summaryData.startDate || '未录入' }}
							<text class="edit-icon">✏️</text>
						</view>
					</view>
					<view class="summary-item">
						<view class="item-label">当前体重</view>
						<view class="item-value" :class="{ 'highlight': summaryData.currentWeight, 'undefined-value': !summaryData.currentWeight }">
							{{ summaryData.currentWeight ? summaryData.currentWeight + 'KG' : '未录入' }}
						</view>
					</view>
				</view>
				<view class="summary-row single-item">
					<view class="summary-item full-width" @click="editInterventionTarget">
						<view class="item-label">干预目标</view>
						<view class="item-value" :class="{ 'target-highlight': summaryData.interventionTarget, 'undefined-value': !summaryData.interventionTarget }">
							{{ summaryData.interventionTarget ? summaryData.interventionTarget + 'KG' : '未录入' }}
							<text class="edit-icon">✏️</text>
						</view>
					</view>
				</view>
				<view class="ai-summary-section">
					<view class="ai-summary-title">
						<text class="ai-icon">🤖</text>
						<text>AI智能总结</text>
					</view>
					<view class="ai-summary-content">
						<text class="summary-text" :class="{ 'undefined-value': !summaryData.aiSummary }">
							{{ summaryData.aiSummary || '暂无总结，请等待AI分析...' }}
						</text>
					</view>
				</view>
				<view class="summary-stats">
					<view class="stats-item">
						<view class="stats-icon">🍽️</view>
						<text>已坚持饮食 <text class="highlight">{{ summaryData.dietDays || 0 }}</text> 天</text>
					</view>
					<view class="stats-item">
						<view class="stats-icon">🏃‍♂️</view>
						<text>运动了 <text class="highlight">{{ summaryData.exerciseDays || 0 }}</text> 天</text>
					</view>
				</view>
			</view>
		</view>

		<view class="punch-card-section">
			<view class="punch-card-header">
				<view class="punch-card-title">打卡记录</view>
				<view class="time-tags">
					<view
						v-for="(item, index) in timeRanges"
						:key="index"
						class="time-tag"
						:class="{ active: selectedTimeRange === item.value }"
						@click="selectTimeRange(item.value)">
						{{ item.label }}
					</view>
				</view>
			</view>
			<view class="calendar-grid">
				<view class="calendar-row" v-for="(week, weekIndex) in calendarWeeks" :key="weekIndex">
					<view
						class="calendar-day"
						v-for="(day, dayIndex) in week"
						:key="dayIndex"
						:class="{ 'has-record': day.hasRecord, 'today': day.isToday }">
						<text class="day-text">{{ day.date }}</text>
						<view v-if="day.hasRecord" class="punch-dot"></view>
					</view>
				</view>
			</view>
		</view>

		<view class="chart-section weight-chart">
			<view class="chart-container">
				<view class="chart-title-overlay">体重变化曲线(KG)</view>
				<canvas
					canvas-id="weightChart"
					id="weightChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color actual-weight"></view>
					<text class="legend-text">实际体重</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<view class="chart-title-overlay">睡眠时长(H)</view>
				<canvas
					canvas-id="sleepChart"
					id="sleepChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color sleep-time"></view>
					<text class="legend-text">睡眠时长</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<view class="chart-title-overlay">饮水(ML)</view>
				<canvas
					canvas-id="waterChart"
					id="waterChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color water-intake"></view>
					<text class="legend-text">饮水量</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<view class="chart-title-overlay">运动时长(小时)</view>
				<canvas
					canvas-id="exerciseChart"
					id="exerciseChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color exercise-time"></view>
					<text class="legend-text">运动时长</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				username: 'yinghk'
			},
			currentDate: '2025-06-09',
			summaryData: {
				startDate: '05/10',
				currentWeight: null, // 测试未录入状态
				interventionTarget: 57.80,
				dietDays: 4,
				exerciseDays: 4,
				aiSummary: '根据您的数据分析，本周体重控制良好，建议继续保持当前的饮食和运动习惯。睡眠质量有待提升，建议每日保证8小时充足睡眠。'
			},
			timeRanges: [
				{ label: '一周', value: 'week' },
				{ label: '两周', value: 'twoWeeks' },
				{ label: '一个月', value: 'month' }
			],
			selectedTimeRange: 'week',
			punchCardData: {
				'week': {
					days: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, true, true, true, true],
					exercise: [false, false, false, true, true, true, true]
				},
				'twoWeeks': {
					days: ['05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, false, false, false, false, false, false, false, true, true, true, true],
					exercise: [false, false, false, false, false, false, false, false, false, false, true, true, true, true]
				},
				'month': {
					days: ['05/10', '05/11', '05/12', '05/13', '05/14', '05/15', '05/16', '05/17', '05/18', '05/19', '05/20', '05/21', '05/22', '05/23', '05/24', '05/25', '05/26', '05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: Array(26).fill(false).concat([true, true, true, true]),
					exercise: Array(26).fill(false).concat([true, true, true, true])
				}
			},
			currentPeriod: 'week', // 'week', 'twoWeeks', 'month'
			chartWidth: 0,
			chartHeight: 0,
			weightChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					actualWeights: [65.8, 65.6, 65.4, 65.2, 65.1, 65.0, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					actualWeights: [66.5, 66.2, 66.0, 65.8, 65.4, 65.2, 65.1, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					actualWeights: [68.0, 67.2, 66.8, 66.0, 65.2, 65.0],
					targetWeights: [64.6, 61.2, 57.8, 57.8, 57.8, 57.8]
				}
			},
			sleepChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					sleepHours: [7.5, 8.0, 7.2, 8.5, 7.8, 8.2, 7.9]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					sleepHours: [7.0, 7.5, 8.0, 7.8, 7.2, 8.5, 7.8, 7.9]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					sleepHours: [6.5, 7.2, 7.8, 8.0, 7.8, 7.9]
				}
			},
			waterChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					waterIntake: [1800, 2000, 1600, 2200, 1900, 2100, 2000]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					waterIntake: [1500, 1800, 2000, 1900, 1600, 2200, 1900, 2000]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					waterIntake: [1200, 1600, 1800, 2000, 1900, 2000]
				}
			},
			exerciseChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					exerciseHours: [0, 0.5, 1.0, 1.5, 1.2, 0.8, 1.0]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					exerciseHours: [0, 0, 0.5, 1.0, 1.0, 1.5, 1.2, 1.0]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					exerciseHours: [0, 0, 0.5, 1.0, 1.2, 1.0]
				}
			}
		}
	},
	computed: {
		summaryTitle() {
			const titleMap = {
				'week': '本周总结',
				'twoWeeks': '两周总结',
				'month': '本月总结'
			};
			return titleMap[this.selectedTimeRange] || '本月总结';
		},
		currentWeekDays() {
			return this.punchCardData[this.selectedTimeRange].days;
		},
		combinedPunchCard() {
			const diet = this.punchCardData[this.selectedTimeRange].diet;
			const exercise = this.punchCardData[this.selectedTimeRange].exercise;
			return diet.map((d, i) => d || exercise[i]);
		},

		calendarWeeks() {
			const days = this.currentWeekDays;
			const punchData = this.combinedPunchCard;
			const today = this.currentDate.replace(/-/g, '/').substring(5); // 转换为 MM/DD 格式

			// 将日期数据转换为日历格式
			const calendarDays = days.map((day, index) => ({
				date: day,
				hasRecord: punchData[index],
				isToday: day === today
			}));

			// 按周分组（每7天一行）
			const weeks = [];
			for (let i = 0; i < calendarDays.length; i += 7) {
				weeks.push(calendarDays.slice(i, i + 7));
			}

			return weeks;
		}
	},
	onLoad() {
		// Load data when the page loads
		this.generateAISummary();
	},
	onReady() {
		this.$nextTick(() => {
			this.initWeightChart();
			this.initSleepChart();
			this.initWaterChart();
			this.initExerciseChart();
		});
	},
	methods: {
		editStartDate() {
			uni.showModal({
				title: '修改起始日期',
				editable: true,
				placeholderText: '请输入日期(如: 05/10)',
				success: (res) => {
					if (res.confirm && res.content) {
						this.summaryData.startDate = res.content;
					}
				}
			});
		},
		editInterventionTarget() {
			uni.showModal({
				title: '修改干预目标',
				editable: true,
				placeholderText: '请输入目标体重(如: 57.80)',
				success: (res) => {
					if (res.confirm && res.content) {
						const weight = parseFloat(res.content);
						if (!isNaN(weight) && weight > 0) {
							this.summaryData.interventionTarget = weight;
						} else {
							uni.showToast({
								title: '请输入有效的体重数值',
								icon: 'none'
							});
						}
					}
				}
			});
		},
		generateAISummary() {
			// 模拟AI生成总结的逻辑
			const summaries = {
				'week': [
					'本周体重控制良好，建议继续保持当前的饮食和运动习惯。睡眠质量有待提升，建议每日保证8小时充足睡眠。',
					'本周运动量适中，体重呈下降趋势。建议增加饮水量，保持良好的新陈代谢。',
					'本周数据显示您的健康管理很有成效，继续坚持现有计划，注意劳逸结合。'
				],
				'twoWeeks': [
					'两周数据分析显示，您的体重管理计划执行良好。建议适当调整运动强度，保持稳定的减重节奏。',
					'两周来的坚持很棒！体重稳步下降，睡眠和运动都有改善。建议继续保持规律作息。',
					'两周总结：整体进展良好，建议在现有基础上增加有氧运动，提高减重效率。'
				],
				'month': [
					'本月体重管理成效显著，已接近阶段性目标。建议保持当前节奏，注意营养均衡。',
					'本月数据表现优秀，各项指标都在改善。建议制定下一阶段的健康目标。',
					'本月总结：减重进展稳定，生活习惯明显改善。建议继续坚持，适当奖励自己。'
				]
			};

			const currentSummaries = summaries[this.selectedTimeRange] || summaries['week'];
			const randomIndex = Math.floor(Math.random() * currentSummaries.length);
			this.summaryData.aiSummary = currentSummaries[randomIndex];
		},
		selectTimeRange(range) {
			this.selectedTimeRange = range;
			// 生成对应时间范围的AI总结
			this.generateAISummary();
			// 更新所有图表数据
			this.updateWeightChart();
			this.updateSleepChart();
			this.updateWaterChart();
			this.updateExerciseChart();
		},
		bindPickerChange(e) {
			this.pickerIndex = e.detail.value;
			if (this.pickerIndex == 0) {
				this.selectedTimeRange = 'week';
			} else if (this.pickerIndex == 1) {
				this.selectedTimeRange = 'twoWeeks';
			} else if (this.pickerIndex == 2) {
				this.selectedTimeRange = 'month';
			}
		},
		initWeightChart() {
			this.initChart('weightChart', 'weight');
		},
		updateWeightChart() {
			this.initChart('weightChart', 'weight');
		},
		initSleepChart() {
			this.initChart('sleepChart', 'sleep');
		},
		updateSleepChart() {
			this.initChart('sleepChart', 'sleep');
		},
		initWaterChart() {
			this.initChart('waterChart', 'water');
		},
		updateWaterChart() {
			this.initChart('waterChart', 'water');
		},
		initExerciseChart() {
			this.initChart('exerciseChart', 'exercise');
		},
		updateExerciseChart() {
			this.initChart('exerciseChart', 'exercise');
		},
		initChart(canvasId, type) {
			const query = uni.createSelectorQuery().in(this);
			query.select(`#${canvasId}`).boundingClientRect(data => {
				if (data) {
					this.chartWidth = data.width;
					this.chartHeight = data.height;
					const ctx = uni.createCanvasContext(canvasId, this);
					this.drawSimpleChart(ctx, type);
				}
			}).exec();
		},
		drawSimpleChart(ctx, type) {
			// 清空画布
			ctx.clearRect(0, 0, this.chartWidth, this.chartHeight);

			// 设置画布背景
			ctx.fillStyle = '#ffffff';
			ctx.fillRect(0, 0, this.chartWidth, this.chartHeight);

			// 获取数据
			let data, colors, unit;
			switch(type) {
				case 'weight':
					data = this.weightChartData[this.selectedTimeRange];
					colors = ['#007bff'];
					unit = 'kg';
					break;
				case 'sleep':
					data = this.sleepChartData[this.selectedTimeRange];
					colors = ['#9c27b0'];
					unit = 'h';
					break;
				case 'water':
					data = this.waterChartData[this.selectedTimeRange];
					colors = ['#2196f3'];
					unit = 'ml';
					break;
				case 'exercise':
					data = this.exerciseChartData[this.selectedTimeRange];
					colors = ['#ff9800'];
					unit = 'h';
					break;
			}

			const { dates } = data;
			const padding = Math.max(20, this.chartWidth * 0.08); // 动态计算边距
			const bottomPadding = Math.max(25, this.chartHeight * 0.15); // 底部留更多空间给日期
			// 所有图表顶部都需要空间给标题
			const topPadding = Math.max(35, this.chartHeight * 0.18);

			const chartArea = {
				left: padding,
				top: topPadding,
				right: this.chartWidth - padding,
				bottom: this.chartHeight - bottomPadding,
				width: this.chartWidth - 2 * padding,
				height: this.chartHeight - topPadding - bottomPadding
			};

			// 绘制日期标签
			ctx.fillStyle = '#666';
			ctx.font = `${Math.max(10, this.chartWidth * 0.03)}px Arial`;
			ctx.textAlign = 'center';
			dates.forEach((date, index) => {
				const x = chartArea.left + (index * chartArea.width) / (dates.length - 1);
				ctx.fillText(date, x, this.chartHeight - 8);
			});

			// 绘制数据
			if (type === 'weight') {
				this.drawWeightData(ctx, chartArea, data, colors, unit);
			} else {
				this.drawSingleLineData(ctx, chartArea, data, colors[0], unit, type);
			}

			ctx.draw();
		},
		drawWeightData(ctx, area, data, colors, unit) {
			const { actualWeights } = data;
			const minWeight = Math.min(...actualWeights) - 1;
			const maxWeight = Math.max(...actualWeights) + 1;
			const weightRange = maxWeight - minWeight;

			// 只绘制实际体重线
			this.drawSimpleLine(ctx, area, actualWeights, minWeight, weightRange, colors[0], unit, 'actual');
		},
		drawSingleLineData(ctx, area, data, color, unit, type) {
			let values;
			switch(type) {
				case 'sleep':
					values = data.sleepHours;
					break;
				case 'water':
					values = data.waterIntake;
					break;
				case 'exercise':
					values = data.exerciseHours;
					break;
			}

			const minValue = Math.min(...values) - (type === 'water' ? 200 : 0.5);
			const maxValue = Math.max(...values) + (type === 'water' ? 200 : 0.5);
			const valueRange = maxValue - minValue;

			this.drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit);
		},
		drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit, lineType = 'single') {
			ctx.strokeStyle = color;
			ctx.lineWidth = 2;
			ctx.fillStyle = color;

			// 绘制折线
			ctx.beginPath();
			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				if (index === 0) {
					ctx.moveTo(x, y);
				} else {
					ctx.lineTo(x, y);
				}
			});
			ctx.stroke();

			// 绘制数据点和标签
			const fontSize = Math.max(8, this.chartWidth * 0.025);
			ctx.font = `${fontSize}px Arial`;
			ctx.textAlign = 'center';
			ctx.fillStyle = color;

			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				// 绘制数据点
				const pointSize = Math.max(2, this.chartWidth * 0.008);
				ctx.beginPath();
				ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
				ctx.fill();

				// 绘制数值标签 - 调整位置避免重叠
				ctx.fillStyle = '#333';
				const displayValue = unit === 'ml' ? value.toString() : value.toFixed(1);
				// 根据数据点位置和线条类型调整标签位置
				const labelOffset = Math.max(10, this.chartHeight * 0.05);
				let labelY;
				if (lineType === 'target') {
					labelY = y + labelOffset; // 目标线标签显示在下方
				} else if (lineType === 'actual') {
					labelY = y - labelOffset * 0.8; // 实际线标签显示在上方
				} else {
					labelY = y < area.height * 0.3 ? y + labelOffset : y - labelOffset * 0.8; // 单线图根据位置调整
				}
				ctx.fillText(displayValue + unit, x, labelY);
				ctx.fillStyle = color;
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20upx;
	background-color: #f0f5f9; /* 浅蓝色调背景 */
	min-height: 100vh;
	font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'microsoft yahei', sans-serif;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30upx;
	padding: 20upx 0;

	.user-info {
		display: flex;
		align-items: center;

		.avatar {
			width: 80upx;
			height: 80upx;
			border-radius: 50%;
			margin-right: 15upx;
			border: 2upx solid #fff; /* 头像边框 */
		}

		.username {
			font-size: 36upx;
			font-weight: bold;
			color: #333;
		}
	}

	.date {
		font-size: 28upx;
		color: #666;
	}
}

.summary-card,
.punch-card-section,
.chart-section {
	background-color: #fff;
	border-radius: 20upx; /* 更圆润的边角 */
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 24upx rgba(0, 0, 0, 0.08); /* 更明显的阴影 */
}

.summary-title,
.punch-card-title,
.chart-title {
	font-size: 36upx; /* 标题字号增大 */
	font-weight: bold;
	margin-bottom: 25upx; /* 标题下边距增大 */
	color: #333;
	text-align: center; /* 标题居中 */
}

.summary-content {
	.summary-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 25upx;

		&.single-item {
			justify-content: center;
		}

		.summary-item {
			flex: 1;
			text-align: center;
			padding: 20upx 15upx;
			background-color: #f8f9fa;
			border-radius: 12upx;
			margin: 0 8upx;
			border: 1upx solid #e9ecef;
			position: relative;
			transition: all 0.3s ease;

			&.full-width {
				max-width: 300upx;
				margin: 0 auto;
			}

			&:active {
				background-color: #e9ecef;
				transform: scale(0.98);
			}

			.item-label {
				font-size: 24upx;
				color: #666;
				margin-bottom: 8upx;
			}

			.item-value {
				font-size: 32upx;
				font-weight: bold;
				color: #333;
				position: relative;

				&.highlight {
					color: #007bff;
				}

				&.target-highlight {
					color: #e40000;
				}

				&.undefined-value {
					color: #999;
					font-style: italic;
					font-weight: normal;
				}

				.edit-icon {
					font-size: 20upx;
					margin-left: 8upx;
					opacity: 0.6;
				}
			}
		}
	}
}

.ai-summary-section {
	margin-top: 30upx;
	padding: 25upx;
	background-color: #f8f9ff;
	border-radius: 15upx;
	border: 1upx solid #e6f0ff;

	.ai-summary-title {
		display: flex;
		align-items: center;
		margin-bottom: 15upx;
		font-size: 28upx;
		font-weight: bold;
		color: #333;

		.ai-icon {
			font-size: 32upx;
			margin-right: 8upx;
		}
	}

	.ai-summary-content {
		.summary-text {
			font-size: 26upx;
			line-height: 1.6;
			color: #555;
			text-align: left;

			&.undefined-value {
				color: #999;
				font-style: italic;
				text-align: center;
			}
		}
	}
}

.summary-stats {
	display: flex;
	justify-content: space-around;
	margin-top: 20upx;
	padding-top: 20upx;
	border-top: 1upx solid #e9ecef;

	.stats-item {
		display: flex;
		align-items: center;
		font-size: 28upx;
		color: #666;

		.stats-icon {
			font-size: 32upx;
			margin-right: 8upx;
		}

		.highlight {
			color: #007bff;
			font-weight: bold;
			margin: 0 6upx;
		}
	}
}

.punch-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25upx;

	.punch-card-title {
		margin-bottom: 0;
		text-align: left;
	}

	.time-tags {
		display: flex;
		gap: 15upx;

		.time-tag {
			padding: 8upx 20upx;
			border-radius: 20upx;
			font-size: 26upx;
			color: #666;
			background-color: #f5f5f5;
			border: 1upx solid #e0e0e0;
			transition: all 0.3s ease;
			cursor: pointer;

			&.active {
				background-color: #007bff;
				color: #ffffff !important;
				border-color: #007bff;
				font-weight: bold;
			}

			&:hover {
				background-color: #e6f3ff;
				color: #007bff;
			}
		}
	}
}

.calendar-grid {
	display: flex;
	flex-direction: column;
	gap: 10upx;

	.calendar-row {
		display: flex;
		justify-content: space-around;
		gap: 8upx;

		.calendar-day {
			flex: 1;
			min-height: 80upx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #f8f9fa;
			border-radius: 12upx;
			border: 1upx solid #e0e0e0;
			position: relative;
			transition: all 0.3s ease;

			&.has-record {
				background-color: #e8f5e8;
				border-color: #4CAF50;
			}

			&.today {
				background-color: #e6f3ff;
				border-color: #007bff;
				border-width: 2upx;
			}

			&.has-record.today {
				background-color: #d4edda;
				border-color: #28a745;
			}

			.day-text {
				font-size: 24upx;
				color: #333;
				font-weight: 500;
			}

			.punch-dot {
				width: 12upx;
				height: 12upx;
				background-color: #4CAF50;
				border-radius: 50%;
				margin-top: 4upx;
			}
		}
	}
}

.chart-section {
	text-align: center;

	.chart-container {
		width: 100%;
		height: 240upx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 15upx;
		overflow: hidden;
		border: 1upx solid #e0e0e0;
		border-radius: 8upx;
		position: relative;

		.chart-canvas {
			width: 100%;
			height: 100%;
		}

		.chart-title-overlay {
			position: absolute;
			top: 8upx;
			left: 50%;
			transform: translateX(-50%);
			font-size: 24upx;
			font-weight: bold;
			color: #333;
			background-color: rgba(255, 255, 255, 0.9);
			padding: 4upx 12upx;
			border-radius: 6upx;
			z-index: 10;
			backdrop-filter: blur(2upx);
			white-space: nowrap;
		}
	}

	.chart-legend {
		display: flex;
		justify-content: center;
		gap: 25upx;
		margin-top: 10upx;
		margin-bottom: 5upx;

		.legend-item {
			display: flex;
			align-items: center;
			gap: 6upx;

			.legend-color {
				width: 14upx;
				height: 14upx;
				border-radius: 2upx;

				&.actual-weight {
					background-color: #007bff;
				}

				&.target-weight {
					background-color: #28a745;
				}

				&.sleep-time {
					background-color: #9c27b0;
				}

				&.water-intake {
					background-color: #2196f3;
				}

				&.exercise-time {
					background-color: #ff9800;
				}
			}

			.legend-text {
				font-size: 22upx;
				color: #666;
			}
		}
	}

	.chart-image {
		width: 100%;
		height: 450upx; /* 图表高度增加 */
		object-fit: contain;
	}
}
</style>
