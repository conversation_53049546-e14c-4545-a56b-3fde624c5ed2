<template>
	<view class="container">
		<view class="header">
			<view class="user-info">
				<image class="avatar" src="/static/icons/user-default.png"></image>
				<text class="username">{{ userInfo.username }}</text>
			</view>
			<view class="date">{{ currentDate }}</view>
		</view>

		<view class="summary-card">
			<view class="summary-title">本月总结</view>
			<view class="summary-table">
				<view class="table-row">
					<view class="table-cell header-cell">日期</view>
					<view class="table-cell" v-for="(date, index) in summaryData.dates" :key="index">{{ date }}</view>
				</view>
				<view class="table-row">
					<view class="table-cell header-cell">体重</view>
					<view class="table-cell" v-for="(weight, index) in summaryData.weights" :key="index">{{ weight }}KG</view>
				</view>
				<view class="table-row">
					<view class="table-cell header-cell">目标</view>
					<view class="table-cell" v-for="(target, index) in summaryData.targets" :key="index">{{ target }}</view>
					<view class="table-cell target-cell">干预目标: {{ summaryData.interventionTarget }}KG</view>
				</view>
			</view>
			<view class="summary-stats">
				<text>已坚持饮食 <text class="highlight">{{ summaryData.dietDays }}</text> 天</text>
				<text>运动了 <text class="highlight">{{ summaryData.exerciseDays }}</text> 天</text>
			</view>
		</view>

		<view class="punch-card-section">
			<view class="punch-card-header">
				<view class="punch-card-title">打卡记录</view>
				<view class="time-tags">
					<view
						v-for="(item, index) in timeRanges"
						:key="index"
						class="time-tag"
						:class="{ active: selectedTimeRange === item.value }"
						@click="selectTimeRange(item.value)">
						{{ item.label }}
					</view>
				</view>
			</view>
			<view class="calendar-grid">
				<view class="calendar-row" v-for="(week, weekIndex) in calendarWeeks" :key="weekIndex">
					<view
						class="calendar-day"
						v-for="(day, dayIndex) in week"
						:key="dayIndex"
						:class="{ 'has-record': day.hasRecord, 'today': day.isToday }">
						<text class="day-text">{{ day.date }}</text>
						<view v-if="day.hasRecord" class="punch-dot"></view>
					</view>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">体重变化曲线(KG)</view>
			<view class="chart-container">
				<canvas
					canvas-id="weightChart"
					id="weightChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color actual-weight"></view>
					<text class="legend-text">实际体重</text>
				</view>
				<view class="legend-item">
					<view class="legend-color target-weight"></view>
					<text class="legend-text">目标体重</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">睡眠时长(H)</view>
			<view class="chart-container">
				<canvas
					canvas-id="sleepChart"
					id="sleepChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color sleep-time"></view>
					<text class="legend-text">睡眠时长</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">饮水(ML)</view>
			<view class="chart-container">
				<canvas
					canvas-id="waterChart"
					id="waterChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color water-intake"></view>
					<text class="legend-text">饮水量</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-title">运动时长(小时)</view>
			<view class="chart-container">
				<canvas
					canvas-id="exerciseChart"
					id="exerciseChart"
					class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color exercise-time"></view>
					<text class="legend-text">运动时长</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			userInfo: {
				username: 'yinghk'
			},
			currentDate: '2025-06-09',
			summaryData: {
				dates: ['05/10', '05/17', '05/24', '06/09'],
				weights: [0, 0, 65, 65],
				targets: ['第一个月 (5%)64.60KG', '第二个月 (5%)61.20KG', '第三个月 (5%)57.80KG'],
				interventionTarget: 57.80,
				dietDays: 4,
				exerciseDays: 4
			},
			timeRanges: [
				{ label: '一周', value: 'week' },
				{ label: '两周', value: 'twoWeeks' },
				{ label: '一个月', value: 'month' }
			],
			selectedTimeRange: 'week',
			punchCardData: {
				'week': {
					days: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, true, true, true, true],
					exercise: [false, false, false, true, true, true, true]
				},
				'twoWeeks': {
					days: ['05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: [false, false, false, false, false, false, false, false, false, false, true, true, true, true],
					exercise: [false, false, false, false, false, false, false, false, false, false, true, true, true, true]
				},
				'month': {
					days: ['05/10', '05/11', '05/12', '05/13', '05/14', '05/15', '05/16', '05/17', '05/18', '05/19', '05/20', '05/21', '05/22', '05/23', '05/24', '05/25', '05/26', '05/27', '05/28', '05/29', '05/30', '05/31', '06/01', '06/02', '06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					diet: Array(26).fill(false).concat([true, true, true, true]),
					exercise: Array(26).fill(false).concat([true, true, true, true])
				}
			},
			currentPeriod: 'week', // 'week', 'twoWeeks', 'month'
			chartWidth: 320,
			chartHeight: 200,
			weightChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					actualWeights: [65.8, 65.6, 65.4, 65.2, 65.1, 65.0, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					actualWeights: [66.5, 66.2, 66.0, 65.8, 65.4, 65.2, 65.1, 65.0],
					targetWeights: [64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6, 64.6]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					actualWeights: [68.0, 67.2, 66.8, 66.0, 65.2, 65.0],
					targetWeights: [64.6, 61.2, 57.8, 57.8, 57.8, 57.8]
				}
			},
			sleepChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					sleepHours: [7.5, 8.0, 7.2, 8.5, 7.8, 8.2, 7.9]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					sleepHours: [7.0, 7.5, 8.0, 7.8, 7.2, 8.5, 7.8, 7.9]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					sleepHours: [6.5, 7.2, 7.8, 8.0, 7.8, 7.9]
				}
			},
			waterChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					waterIntake: [1800, 2000, 1600, 2200, 1900, 2100, 2000]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					waterIntake: [1500, 1800, 2000, 1900, 1600, 2200, 1900, 2000]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					waterIntake: [1200, 1600, 1800, 2000, 1900, 2000]
				}
			},
			exerciseChartData: {
				'week': {
					dates: ['06/03', '06/04', '06/05', '06/06', '06/07', '06/08', '06/09'],
					exerciseHours: [0, 0.5, 1.0, 1.5, 1.2, 0.8, 1.0]
				},
				'twoWeeks': {
					dates: ['05/27', '05/29', '05/31', '06/02', '06/04', '06/06', '06/08', '06/09'],
					exerciseHours: [0, 0, 0.5, 1.0, 1.0, 1.5, 1.2, 1.0]
				},
				'month': {
					dates: ['05/10', '05/17', '05/24', '05/31', '06/07', '06/09'],
					exerciseHours: [0, 0, 0.5, 1.0, 1.2, 1.0]
				}
			}
		}
	},
	computed: {
		currentWeekDays() {
			return this.punchCardData[this.selectedTimeRange].days;
		},
		combinedPunchCard() {
			const diet = this.punchCardData[this.selectedTimeRange].diet;
			const exercise = this.punchCardData[this.selectedTimeRange].exercise;
			return diet.map((d, i) => d || exercise[i]);
		},
		calendarWeeks() {
			const days = this.currentWeekDays;
			const punchData = this.combinedPunchCard;
			const today = this.currentDate.replace(/-/g, '/').substring(5); // 转换为 MM/DD 格式

			// 将日期数据转换为日历格式
			const calendarDays = days.map((day, index) => ({
				date: day,
				hasRecord: punchData[index],
				isToday: day === today
			}));

			// 按周分组（每7天一行）
			const weeks = [];
			for (let i = 0; i < calendarDays.length; i += 7) {
				weeks.push(calendarDays.slice(i, i + 7));
			}

			return weeks;
		}
	},
	onLoad() {
		// Load data when the page loads
	},
	onReady() {
		this.$nextTick(() => {
			this.initWeightChart();
			this.initSleepChart();
			this.initWaterChart();
			this.initExerciseChart();
		});
	},
	methods: {
		selectTimeRange(range) {
			this.selectedTimeRange = range;
			// 更新所有图表数据
			this.updateWeightChart();
			this.updateSleepChart();
			this.updateWaterChart();
			this.updateExerciseChart();
		},
		bindPickerChange(e) {
			this.pickerIndex = e.detail.value;
			if (this.pickerIndex == 0) {
				this.selectedTimeRange = 'week';
			} else if (this.pickerIndex == 1) {
				this.selectedTimeRange = 'twoWeeks';
			} else if (this.pickerIndex == 2) {
				this.selectedTimeRange = 'month';
			}
		},
		initWeightChart() {
			const ctx = uni.createCanvasContext('weightChart', this);
			this.drawSimpleChart(ctx, 'weight');
		},
		updateWeightChart() {
			const ctx = uni.createCanvasContext('weightChart', this);
			this.drawSimpleChart(ctx, 'weight');
		},
		initSleepChart() {
			const ctx = uni.createCanvasContext('sleepChart', this);
			this.drawSimpleChart(ctx, 'sleep');
		},
		updateSleepChart() {
			const ctx = uni.createCanvasContext('sleepChart', this);
			this.drawSimpleChart(ctx, 'sleep');
		},
		initWaterChart() {
			const ctx = uni.createCanvasContext('waterChart', this);
			this.drawSimpleChart(ctx, 'water');
		},
		updateWaterChart() {
			const ctx = uni.createCanvasContext('waterChart', this);
			this.drawSimpleChart(ctx, 'water');
		},
		initExerciseChart() {
			const ctx = uni.createCanvasContext('exerciseChart', this);
			this.drawSimpleChart(ctx, 'exercise');
		},
		updateExerciseChart() {
			const ctx = uni.createCanvasContext('exerciseChart', this);
			this.drawSimpleChart(ctx, 'exercise');
		},
		drawSimpleChart(ctx, type) {
			// 清空画布
			ctx.clearRect(0, 0, this.chartWidth, this.chartHeight);

			// 设置画布背景
			ctx.fillStyle = '#ffffff';
			ctx.fillRect(0, 0, this.chartWidth, this.chartHeight);

			// 获取数据
			let data, colors, unit;
			switch(type) {
				case 'weight':
					data = this.weightChartData[this.selectedTimeRange];
					colors = ['#007bff', '#28a745'];
					unit = 'kg';
					break;
				case 'sleep':
					data = this.sleepChartData[this.selectedTimeRange];
					colors = ['#9c27b0'];
					unit = 'h';
					break;
				case 'water':
					data = this.waterChartData[this.selectedTimeRange];
					colors = ['#2196f3'];
					unit = 'ml';
					break;
				case 'exercise':
					data = this.exerciseChartData[this.selectedTimeRange];
					colors = ['#ff9800'];
					unit = 'h';
					break;
			}

			const { dates } = data;
			const padding = 30;
			const chartArea = {
				left: padding,
				top: padding + 20,
				right: this.chartWidth - padding,
				bottom: this.chartHeight - padding - 25,
				width: this.chartWidth - 2 * padding,
				height: this.chartHeight - 2 * padding - 45
			};

			// 绘制日期标签
			ctx.fillStyle = '#666';
			ctx.font = '11px Arial';
			ctx.textAlign = 'center';
			dates.forEach((date, index) => {
				const x = chartArea.left + (index * chartArea.width) / (dates.length - 1);
				ctx.fillText(date, x, this.chartHeight - 8);
			});

			// 绘制数据
			if (type === 'weight') {
				this.drawWeightData(ctx, chartArea, data, colors, unit);
			} else {
				this.drawSingleLineData(ctx, chartArea, data, colors[0], unit, type);
			}

			ctx.draw();
		},
		drawWeightData(ctx, area, data, colors, unit) {
			const { actualWeights, targetWeights } = data;
			const allWeights = [...actualWeights, ...targetWeights];
			const minWeight = Math.min(...allWeights) - 1;
			const maxWeight = Math.max(...allWeights) + 1;
			const weightRange = maxWeight - minWeight;

			// 绘制目标体重线（先绘制，避免被实际体重线覆盖）
			this.drawSimpleLine(ctx, area, targetWeights, minWeight, weightRange, colors[1], unit, 'target');
			// 绘制实际体重线
			this.drawSimpleLine(ctx, area, actualWeights, minWeight, weightRange, colors[0], unit, 'actual');
		},
		drawSingleLineData(ctx, area, data, color, unit, type) {
			let values;
			switch(type) {
				case 'sleep':
					values = data.sleepHours;
					break;
				case 'water':
					values = data.waterIntake;
					break;
				case 'exercise':
					values = data.exerciseHours;
					break;
			}

			const minValue = Math.min(...values) - (type === 'water' ? 200 : 0.5);
			const maxValue = Math.max(...values) + (type === 'water' ? 200 : 0.5);
			const valueRange = maxValue - minValue;

			this.drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit);
		},
		drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit, lineType = 'single') {
			ctx.strokeStyle = color;
			ctx.lineWidth = 2;
			ctx.fillStyle = color;

			// 绘制折线
			ctx.beginPath();
			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				if (index === 0) {
					ctx.moveTo(x, y);
				} else {
					ctx.lineTo(x, y);
				}
			});
			ctx.stroke();

			// 绘制数据点和标签
			ctx.font = '9px Arial';
			ctx.textAlign = 'center';
			ctx.fillStyle = color;

			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				// 绘制数据点
				ctx.beginPath();
				ctx.arc(x, y, 3, 0, 2 * Math.PI);
				ctx.fill();

				// 绘制数值标签 - 调整位置避免重叠
				ctx.fillStyle = '#333';
				const displayValue = unit === 'ml' ? value.toString() : value.toFixed(1);
				// 根据数据点位置和线条类型调整标签位置
				let labelY;
				if (lineType === 'target') {
					labelY = y + 15; // 目标线标签显示在下方
				} else if (lineType === 'actual') {
					labelY = y - 10; // 实际线标签显示在上方
				} else {
					labelY = y < 40 ? y + 15 : y - 10; // 单线图根据位置调整
				}
				ctx.fillText(displayValue + unit, x, labelY);
				ctx.fillStyle = color;
			});
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20upx;
	background-color: #f0f5f9; /* 浅蓝色调背景 */
	min-height: 100vh;
	font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'microsoft yahei', sans-serif;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30upx;
	padding: 20upx 0;

	.user-info {
		display: flex;
		align-items: center;

		.avatar {
			width: 80upx;
			height: 80upx;
			border-radius: 50%;
			margin-right: 15upx;
			border: 2upx solid #fff; /* 头像边框 */
		}

		.username {
			font-size: 36upx;
			font-weight: bold;
			color: #333;
		}
	}

	.date {
		font-size: 28upx;
		color: #666;
	}
}

.summary-card,
.punch-card-section,
.chart-section {
	background-color: #fff;
	border-radius: 20upx; /* 更圆润的边角 */
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 24upx rgba(0, 0, 0, 0.08); /* 更明显的阴影 */
}

.summary-title,
.punch-card-title,
.chart-title {
	font-size: 36upx; /* 标题字号增大 */
	font-weight: bold;
	margin-bottom: 25upx; /* 标题下边距增大 */
	color: #333;
	text-align: center; /* 标题居中 */
}

.summary-table {
	display: table;
	width: 100%;
	border-collapse: collapse;
	margin-bottom: 20upx;

	.table-row {
		display: table-row;

		.table-cell {
			display: table-cell;
			padding: 18upx 10upx; /* 单元格内边距增大 */
			border: 1upx solid #e0e0e0; /* 边框颜色变浅 */
			text-align: center;
			font-size: 28upx; /* 字体增大 */
			color: #555;

			&.header-cell {
				background-color: #eaf4f9; /* 表头背景色 */
				font-weight: bold;
				color: #333;
			}

			&.target-cell {
				background-color: #ffebeb; /* 目标背景色 */
				color: #e40000;
				font-weight: bold;
			}
		}
	}
}

.summary-stats {
	display: flex;
	justify-content: space-around;
	font-size: 30upx; /* 字体增大 */
	color: #666;
	padding-top: 15upx; /* 增加上边距 */

	.highlight {
		color: #007bff; /* 蓝色高亮 */
		font-weight: bold;
		margin: 0 8upx; /* 间距增大 */
	}
}

.punch-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25upx;

	.punch-card-title {
		margin-bottom: 0;
		text-align: left;
	}

	.time-tags {
		display: flex;
		gap: 15upx;

		.time-tag {
			padding: 8upx 20upx;
			border-radius: 20upx;
			font-size: 26upx;
			color: #666;
			background-color: #f5f5f5;
			border: 1upx solid #e0e0e0;
			transition: all 0.3s ease;
			cursor: pointer;

			&.active {
				background-color: #007bff;
				color: #ffffff !important;
				border-color: #007bff;
				font-weight: bold;
			}

			&:hover {
				background-color: #e6f3ff;
				color: #007bff;
			}
		}
	}
}

.calendar-grid {
	display: flex;
	flex-direction: column;
	gap: 10upx;

	.calendar-row {
		display: flex;
		justify-content: space-around;
		gap: 8upx;

		.calendar-day {
			flex: 1;
			min-height: 80upx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #f8f9fa;
			border-radius: 12upx;
			border: 1upx solid #e0e0e0;
			position: relative;
			transition: all 0.3s ease;

			&.has-record {
				background-color: #e8f5e8;
				border-color: #4CAF50;
			}

			&.today {
				background-color: #e6f3ff;
				border-color: #007bff;
				border-width: 2upx;
			}

			&.has-record.today {
				background-color: #d4edda;
				border-color: #28a745;
			}

			.day-text {
				font-size: 24upx;
				color: #333;
				font-weight: 500;
			}

			.punch-dot {
				width: 12upx;
				height: 12upx;
				background-color: #4CAF50;
				border-radius: 50%;
				margin-top: 4upx;
			}
		}
	}
}

.chart-section {
	text-align: center;

	.chart-title {
		color: #333;
	}

	.chart-container {
		width: 100%;
		height: 240upx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 15upx;
		overflow: hidden;

		.chart-canvas {
			width: 320upx;
			height: 200upx;
			border: 1upx solid #e0e0e0;
			border-radius: 8upx;
		}
	}

	.chart-legend {
		display: flex;
		justify-content: center;
		gap: 25upx;
		margin-top: 10upx;
		margin-bottom: 5upx;

		.legend-item {
			display: flex;
			align-items: center;
			gap: 6upx;

			.legend-color {
				width: 14upx;
				height: 14upx;
				border-radius: 2upx;

				&.actual-weight {
					background-color: #007bff;
				}

				&.target-weight {
					background-color: #28a745;
				}

				&.sleep-time {
					background-color: #9c27b0;
				}

				&.water-intake {
					background-color: #2196f3;
				}

				&.exercise-time {
					background-color: #ff9800;
				}
			}

			.legend-text {
				font-size: 22upx;
				color: #666;
			}
		}
	}

	.chart-image {
		width: 100%;
		height: 450upx; /* 图表高度增加 */
		object-fit: contain;
	}
}
</style>
