<template>
	<view class="container">
		<view class="header">
			<view class="header-left">
				<view class="action-buttons">
					<view class="action-btn back-btn" @click="goBack">
						<text class="btn-icon">←</text>
						<text class="btn-text">返回</text>
					</view>
					<view class="action-btn refresh-btn" @click="refreshData">
						<text class="btn-icon">↻</text>
						<text class="btn-text">刷新</text>
					</view>
				</view>
			</view>
			<view class="header-center">
				<view class="user-info">
					<image class="avatar" :src="userInfo.avatar || userInfo.avatarUrl || '/static/icons/user-default.png'">
					</image>
					<text class="username">{{ userInfo.nickName }}</text>
				</view>
			</view>
			<view class="header-right">
				<view class="date">{{ currentDate }}</view>
			</view>
		</view>

		<view class="summary-card">
			<view class="summary-title">{{ summaryTitle }}</view>
			<view class="summary-content">
				<view class="ai-summary-section">
					<view class="ai-summary-title">
						<text class="ai-icon">🤖</text>
						<text>AI智能总结</text>
					</view>
					<view class="ai-summary-content">
						<text class="summary-text" :class="{ 'undefined-value': !summaryData.aiSummary }">
							{{ summaryData.aiSummary || '暂无总结，请等待AI分析...' }}
						</text>
					</view>
				</view>
				<view class="summary-row">
					<view class="summary-item" @click="editStartDate">
						<view class="item-label">起始日期</view>
						<view class="item-value" :class="{ 'undefined-value': !summaryData.startDate }">
							{{ summaryData.startDate || '未录入' }}
							<text class="edit-icon">✏️</text>
						</view>
					</view>
					<view class="summary-item">
						<view class="item-label">当前体重</view>
						<view class="item-value"
							:class="{ 'highlight': summaryData.currentWeight, 'undefined-value': !summaryData.currentWeight }">
							{{ summaryData.currentWeight ? summaryData.currentWeight + 'KG' : '未录入' }}
						</view>
					</view>
				</view>
				<view class="summary-row single-item">
					<view class="summary-item full-width" @click="editInterventionTarget">
						<view class="item-label">干预目标</view>
						<view class="item-value"
							:class="{ 'target-highlight': summaryData.interventionTarget, 'undefined-value': !summaryData.interventionTarget }">
							{{ summaryData.interventionTarget ? summaryData.interventionTarget + 'KG' : '未录入' }}
							<text class="edit-icon">✏️</text>
						</view>
					</view>
				</view>
				<view class="summary-stats">
					<view class="stats-item">
						<view class="stats-icon">🍽️</view>
						<text>已坚持饮食 <text class="highlight">{{ summaryData.dietDays || 0 }}</text> 天</text>
					</view>
					<view class="stats-item">
						<view class="stats-icon">🏃‍♂️</view>
						<text>运动了 <text class="highlight">{{ summaryData.exerciseDays || 0 }}</text> 天</text>
					</view>
				</view>
			</view>
		</view>

		<view class="punch-card-section">
			<view class="punch-card-header">
				<view class="punch-card-title-container">
					<view class="punch-card-title">打卡记录</view>
					<view v-if="dateRange.startDate && dateRange.endDate" class="date-range-display">
						{{ formatDateRange(dateRange.startDate) }} - {{ formatDateRange(dateRange.endDate) }}
					</view>
				</view>
				<view class="time-tags">
					<view v-for="(item, index) in timeRanges" :key="index" class="time-tag"
						:class="{ active: selectedTimeRange === item.value }" @click="selectTimeRange(item.value)">
						{{ item.label }}
					</view>
				</view>
			</view>

			<!-- 自定义天数输入框 -->
			<view v-if="selectedTimeRange === 'other'" class="custom-days-input">
				<view class="input-container">
					<view class="input-label">从今天开始往前：</view>
					<input
						type="number"
						v-model="customDays"
						placeholder="请输入天数"
						class="days-input"
						@blur="updateCustomDays"
					/>
					<text class="input-unit">天</text>
					<view class="confirm-btn" @click="applyCustomDays">确定</view>
				</view>
			</view>
			<view class="calendar-grid">
				<view v-if="calendarWeeks.length === 0 || (calendarWeeks.length === 1 && calendarWeeks[0].length === 0)" class="no-data-message">
					<view class="no-data-icon">📅</view>
					<view class="no-data-text">暂无打卡记录</view>
					<view class="no-data-tip">开始记录您的健康数据吧</view>
				</view>
				<view v-else class="calendar-row" v-for="(week, weekIndex) in calendarWeeks" :key="weekIndex">
					<view class="calendar-day" v-for="(day, dayIndex) in week" :key="dayIndex"
						:class="{ 'today': day.isToday }">
						<text class="day-text">{{ day.date }}</text>
						<view class="punch-dots">
							<view v-if="day.hasDiet" class="punch-dot diet-dot"></view>
							<view v-if="day.hasExercise" class="punch-dot exercise-dot"></view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<canvas canvas-id="weightChart" id="weightChart" class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color actual-weight"></view>
					<text class="legend-text">实际体重</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<canvas canvas-id="sleepChart" id="sleepChart" class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color sleep-time"></view>
					<text class="legend-text">睡眠时长</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<canvas canvas-id="waterChart" id="waterChart" class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color water-intake"></view>
					<text class="legend-text">饮水量</text>
				</view>
			</view>
		</view>

		<view class="chart-section">
			<view class="chart-container">
				<canvas canvas-id="exerciseChart" id="exerciseChart" class="chart-canvas">
				</canvas>
			</view>
			<view class="chart-legend">
				<view class="legend-item">
					<view class="legend-color exercise-time"></view>
					<text class="legend-text">运动时长</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import groupx from "@/api/api-groupx";
import myStorage from "../../common/my-storage"

export default {
	data() {
		return {
			userInfo: myStorage.getUserInfo(),
			userObjectId: myStorage.getUserObjectId(),
			groupObjectId: myStorage.getGroupObjectId(),
			currentDate: '2025-06-09',
			summaryData: {
				startDate: null,
				currentWeight: null,
				interventionTarget: null,
				dietDays: 0,
				exerciseDays: 0,
				aiSummary: null
			},
			timeRanges: [
				{ label: '一周', value: 'week' },
				{ label: '两周', value: 'twoWeeks' },
				{ label: '一个月', value: 'month' },
				{ label: '其他', value: 'other' }
			],
			selectedTimeRange: 'week', // 默认值，会被用户偏好覆盖
			searchData: {
				'week': { chart_type: '周报', lastDays: 7 },
				'twoWeeks': { chart_type: '两周报', lastDays: 14 },
				'month': { chart_type: '月报', lastDays: 30 },
				'other': { chart_type: '其他', lastDays: 30 }
			},
			punchCardData: {
				'week': { days: [], diet: [], exercise: [] },
				'twoWeeks': { days: [], diet: [], exercise: [] },
				'month': { days: [], diet: [], exercise: [] },
				'other': { days: [], diet: [], exercise: [] }
			},
			currentPeriod: 'week', // 'week', 'twoWeeks', 'month'
			chartWidth: 0,
			chartHeight: 0,
			customDays: 30, // 自定义天数，默认30天
			showCustomDaysInput: false, // 是否显示自定义天数输入框
			dateRange: { // 存储服务器返回的日期范围
				startDate: null,
				endDate: null
			},
			weightChartData: {
				'week': { dates: [], actualWeights: [] },
				'twoWeeks': { dates: [], actualWeights: [] },
				'month': { dates: [], actualWeights: [] },
				'other': { dates: [], actualWeights: [] }
			},
			sleepChartData: {
				'week': { dates: [], sleepHours: [] },
				'twoWeeks': { dates: [], sleepHours: [] },
				'month': { dates: [], sleepHours: [] },
				'other': { dates: [], sleepHours: [] }
			},
			waterChartData: {
				'week': { dates: [], waterIntake: [] },
				'twoWeeks': { dates: [], waterIntake: [] },
				'month': { dates: [], waterIntake: [] },
				'other': { dates: [], waterIntake: [] }
			},
			exerciseChartData: {
				'week': { dates: [], exerciseHours: [] },
				'twoWeeks': { dates: [], exerciseHours: [] },
				'month': { dates: [], exerciseHours: [] },
				'other': { dates: [], exerciseHours: [] }
			}
		}
	},
	computed: {
		summaryTitle() {
			const titleMap = {
				'week': '本周总结',
				'twoWeeks': '两周总结',
				'month': '本月总结'
			};
			return titleMap[this.selectedTimeRange] || '本月总结';
		},
		currentWeekDays() {
			return this.punchCardData[this.selectedTimeRange].days;
		},
		combinedPunchCard() {
			const diet = this.punchCardData[this.selectedTimeRange].diet;
			const exercise = this.punchCardData[this.selectedTimeRange].exercise;
			return diet.map((d, i) => d || exercise[i]);
		},

		calendarWeeks() {
			const days = this.currentWeekDays;
			const dietData = this.punchCardData[this.selectedTimeRange].diet;
			const exerciseData = this.punchCardData[this.selectedTimeRange].exercise;
			const today = this.currentDate.replace(/-/g, '/').substring(5); // 转换为 MM/DD 格式

			// 将日期数据转换为日历格式
			const calendarDays = days.map((day, index) => ({
				date: day,
				hasDiet: dietData[index],
				hasExercise: exerciseData[index],
				isToday: day === today
			}));

			// 按周分组（每7天一行）
			const weeks = [];
			for (let i = 0; i < calendarDays.length; i += 7) {
				weeks.push(calendarDays.slice(i, i + 7));
			}

			return weeks;
		}
	},
	onLoad() {
		// 首先读取用户的时间范围偏好
		this.loadTimeRangePreference();

		// Load data when the page loads
		const userOID = this.userObjectId || this.userInfo?.userObjectId
		const groupID = this.groupObjectId || this.userInfo?.groupObjectId
		console.log("userInfo:", this.userInfo)
		console.log("userOID,groupID:", userOID, groupID)
		groupx.fetchWeightLosssLastDay({
			agent: this.userInfo?.agent,
			userObjectId: userOID,

			lastDays: this.searchData[this.selectedTimeRange].lastDays,
			chart_type: this.searchData[this.selectedTimeRange].chart_type,

			itchatUser: { "objectId": userOID, "NickName": this.userInfo?.nickName },
			itchatGroup: { "objectId": groupID }
		}).then(response => {
			console.log('服务器返回数据:', response);
			this.processServerData(response);
		}).catch(error => {
			console.error('获取数据失败:', error);
			// 即使获取失败也要更新标题
			this.updateSummaryTitle();
		})
	},
	onReady() {
		this.$nextTick(() => {
			this.initWeightChart();
			this.initSleepChart();
			this.initWaterChart();
			this.initExerciseChart();
		});
		console.log("UserInfo:", this.userInfo)
	},
	methods: {
		goBack() {
			uni.navigateBack({
				delta: 1
			});
		},
		refreshData() {
			uni.showLoading({
				title: '刷新中...'
			});

			// 重新获取服务器数据
			const userOID = this.userObjectId || this.userInfo?.userObjectId;
			const groupID = this.groupObjectId || this.userInfo?.groupObjectId;

			groupx.fetchWeightLosssLastDay({
				agent: this.userInfo?.agent,
				userObjectId: userOID,
				lastDays: this.searchData[this.selectedTimeRange].lastDays,
				chart_type: this.searchData[this.selectedTimeRange].chart_type,
				itchatUser: { "objectId": userOID, "NickName": this.userInfo?.nickName },
				itchatGroup: { "objectId": groupID }
			}).then(response => {
				console.log('刷新数据成功:', response);
				this.processServerData(response);
				uni.hideLoading();
				uni.showToast({
					title: '刷新成功',
					icon: 'success',
					duration: 1500
				});
			}).catch(error => {
				console.error('刷新数据失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '刷新失败',
					icon: 'error',
					duration: 1500
				});
			});
		},
		processServerData(response) {
			if (response.error !== 0) {
				console.error('服务器返回错误:', response);
				return;
			}

			// 存储服务器返回的日期范围
			if (response.startDate && response.endDate) {
				this.dateRange.startDate = response.startDate;
				this.dateRange.endDate = response.endDate;
			}

			// 处理用户卡片信息
			const userCard = response.userCard;
			if (userCard) {
				// 更新总结数据
				this.summaryData.startDate = this.formatDate(userCard.firstWeightDate);
				this.summaryData.currentWeight = userCard.lastWeight;
				this.summaryData.interventionTarget = userCard.targetWeight || userCard.firstWeight * (1 - userCard.targetWeightMonth);
			}

			// 处理打卡数据
			const data = response.data || [];
			this.processChartData(data);
			this.processPunchCardData(data);

			// 使用服务器返回的showText2作为AI总结
			if (response.showText2) {
				this.summaryData.aiSummary = response.showText2;
			} else {
				this.summaryData.aiSummary = '暂无总结，请等待AI分析...';
			}

			// 更新总结标题以反映当前时间范围
			this.updateSummaryTitle();

			// 重新绘制图表
			this.$nextTick(() => {
				this.updateWeightChart();
				this.updateSleepChart();
				this.updateWaterChart();
				this.updateExerciseChart();
			});
		},

		processChartData(data) {
			// 如果没有数据，清空所有图表数据
			if (!data || data.length === 0) {
				const timeRange = this.selectedTimeRange;
				this.weightChartData[timeRange] = { dates: [], actualWeights: [] };
				this.sleepChartData[timeRange] = { dates: [], sleepHours: [] };
				this.waterChartData[timeRange] = { dates: [], waterIntake: [] };
				this.exerciseChartData[timeRange] = { dates: [], exerciseHours: [] };
				return;
			}

			// 按日期排序
			const sortedData = data.sort((a, b) => new Date(a.activityDate) - new Date(b.activityDate));

			// 提取数据
			const dates = [];
			const weights = [];
			const sleepHours = [];
			const waterIntake = [];
			const exerciseHours = [];

			sortedData.forEach(item => {
				const date = this.formatDate(item.activityDate);
				dates.push(date);
				weights.push(item.weight || 0);
				sleepHours.push((item.sleepTime || 0) / 60); // 转换为小时
				waterIntake.push(item.waterIntake || 0);
				exerciseHours.push((item.sportsTime || 0) / 60); // 转换为小时
			});

			// 更新图表数据
			const timeRange = this.selectedTimeRange;
			this.weightChartData[timeRange] = {
				dates: dates,
				actualWeights: weights
			};

			this.sleepChartData[timeRange] = {
				dates: dates,
				sleepHours: sleepHours
			};

			this.waterChartData[timeRange] = {
				dates: dates,
				waterIntake: waterIntake
			};

			this.exerciseChartData[timeRange] = {
				dates: dates,
				exerciseHours: exerciseHours
			};
		},

		processPunchCardData(data) {
			// 如果没有数据，清空打卡数据
			if (!data || data.length === 0) {
				const timeRange = this.selectedTimeRange;
				this.punchCardData[timeRange] = { days: [], diet: [], exercise: [] };
				this.summaryData.dietDays = 0;
				this.summaryData.exerciseDays = 0;
				return;
			}

			// 按日期排序
			const sortedData = data.sort((a, b) => new Date(a.activityDate) - new Date(b.activityDate));

			const dates = [];
			const diet = [];
			const exercise = [];

			sortedData.forEach(item => {
				const date = this.formatDate(item.activityDate);
				dates.push(date);

				// 判断是否有饮食记录（有体重记录或饮水记录）
				const hasDiet = (item.weight && item.weight > 0) || (item.waterIntake && item.waterIntake > 0);
				diet.push(hasDiet);

				// 判断是否有运动记录
				const hasExercise = (item.sportsTime && item.sportsTime > 0) ||
								   (item.exerciseAerobic && item.exerciseAerobic > 0) ||
								   (item.exerciseResistance && item.exerciseResistance > 0) ||
								   (item.exerciseFlexibility && item.exerciseFlexibility > 0);
				exercise.push(hasExercise);
			});

			// 更新打卡数据
			const timeRange = this.selectedTimeRange;
			this.punchCardData[timeRange] = {
				days: dates,
				diet: diet,
				exercise: exercise
			};

			// 更新统计数据
			this.summaryData.dietDays = diet.filter(d => d).length;
			this.summaryData.exerciseDays = exercise.filter(e => e).length;
		},

		formatDate(dateString) {
			const date = new Date(dateString);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}/${day}`;
		},

		formatDateRange(dateString) {
			const date = new Date(dateString);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}/${day}`;
		},

		updateCustomDays() {
			// 验证输入的天数
			if (this.customDays < 1) {
				this.customDays = 1;
			} else if (this.customDays > 365) {
				this.customDays = 365;
			}
		},

		applyCustomDays() {
			// 验证并应用自定义天数
			this.updateCustomDays();

			// 更新搜索参数
			this.searchData.other.lastDays = this.customDays;

			// 保存自定义天数到本地存储
			this.saveCustomDays();

			// 显示加载提示
			uni.showLoading({
				title: '加载数据中...'
			});

			// 重新获取数据
			this.fetchDataForTimeRange('other');
		},

		saveTimeRangePreference() {
			// 保存用户选择的时间范围偏好
			try {
				uni.setStorageSync('selectedTimeRange', this.selectedTimeRange);
				if (this.selectedTimeRange === 'other') {
					uni.setStorageSync('customDays', this.customDays);
				}
			} catch (error) {
				console.error('保存时间范围偏好失败:', error);
			}
		},

		loadTimeRangePreference() {
			// 读取用户选择的时间范围偏好
			try {
				const savedTimeRange = uni.getStorageSync('selectedTimeRange');
				if (savedTimeRange && this.timeRanges.some(item => item.value === savedTimeRange)) {
					this.selectedTimeRange = savedTimeRange;
				}

				// 如果是自定义选项，也读取自定义天数
				if (this.selectedTimeRange === 'other') {
					const savedCustomDays = uni.getStorageSync('customDays');
					if (savedCustomDays && savedCustomDays > 0) {
						this.customDays = savedCustomDays;
						this.searchData.other.lastDays = savedCustomDays;
					}
				}
			} catch (error) {
				console.error('读取时间范围偏好失败:', error);
			}
		},

		saveCustomDays() {
			// 保存自定义天数
			try {
				uni.setStorageSync('customDays', this.customDays);
			} catch (error) {
				console.error('保存自定义天数失败:', error);
			}
		},

		editStartDate() {
			uni.showModal({
				title: '修改起始日期',
				editable: true,
				placeholderText: '请输入日期(如: 05/10)',
				success: (res) => {
					if (res.confirm && res.content) {
						this.summaryData.startDate = res.content;
					}
				}
			});
		},
		editInterventionTarget() {
			uni.showModal({
				title: '修改干预目标',
				editable: true,
				placeholderText: '请输入目标体重(如: 57.80)',
				success: (res) => {
					if (res.confirm && res.content) {
						const weight = parseFloat(res.content);
						if (!isNaN(weight) && weight > 0) {
							this.summaryData.interventionTarget = weight;
						} else {
							uni.showToast({
								title: '请输入有效的体重数值',
								icon: 'none'
							});
						}
					}
				}
			});
		},

		selectTimeRange(range) {
			if (this.selectedTimeRange === range) {
				return; // 如果选择的是当前范围，不需要重新获取
			}

			this.selectedTimeRange = range;

			// 保存用户选择的时间范围偏好
			this.saveTimeRangePreference();

			// 如果选择的是"其他"，先显示输入框，不立即获取数据
			if (range === 'other') {
				this.showCustomDaysInput = true;
				// 使用当前的自定义天数更新搜索参数
				this.searchData.other.lastDays = this.customDays;
			}

			// 显示加载提示
			uni.showLoading({
				title: '加载数据中...'
			});

			// 重新向服务器获取对应时间范围的数据
			this.fetchDataForTimeRange(range);
		},

		updateSummaryTitle() {
			const titleMap = {
				'week': '本周总结',
				'twoWeeks': '两周总结',
				'month': '本月总结'
			};

			if (this.selectedTimeRange === 'other') {
				// 为"其他"选项生成动态标题
				const days = this.customDays || 30;
				this.summaryTitle = `近${days}天总结`;
			} else {
				this.summaryTitle = titleMap[this.selectedTimeRange] || '总结';
			}
		},

		fetchDataForTimeRange(range) {
			const userOID = this.userObjectId || this.userInfo?.userObjectId;
			const groupID = this.groupObjectId || this.userInfo?.groupObjectId;

			// 根据时间范围获取对应的搜索参数
			const searchParams = this.searchData[range];
			if (!searchParams) {
				console.error('未找到对应时间范围的搜索参数:', range);
				uni.hideLoading();
				return;
			}

			groupx.fetchWeightLosssLastDay({
				agent: this.userInfo?.agent,
				userObjectId: userOID,
				lastDays: searchParams.lastDays,
				chart_type: searchParams.chart_type,
				itchatUser: { "objectId": userOID, "NickName": this.userInfo?.nickName },
				itchatGroup: { "objectId": groupID }
			}).then(response => {
				console.log(`${range}时间范围数据获取成功:`, response);
				this.processServerData(response);
				uni.hideLoading();

				// 显示成功提示
				uni.showToast({
					title: '数据加载完成',
					icon: 'success',
					duration: 1000
				});
			}).catch(error => {
				console.error(`${range}时间范围数据获取失败:`, error);
				uni.hideLoading();

				// 显示错误提示
				uni.showToast({
					title: '数据加载失败',
					icon: 'error',
					duration: 1500
				});

				// 如果获取失败，设置默认总结
				this.summaryData.aiSummary = '数据获取失败，请稍后重试...';
			});
		},

		bindPickerChange(e) {
			this.pickerIndex = e.detail.value;
			if (this.pickerIndex == 0) {
				this.selectedTimeRange = 'week';
			} else if (this.pickerIndex == 1) {
				this.selectedTimeRange = 'twoWeeks';
			} else if (this.pickerIndex == 2) {
				this.selectedTimeRange = 'month';
			}
		},
		initWeightChart() {
			this.initChart('weightChart', 'weight');
		},
		updateWeightChart() {
			this.initChart('weightChart', 'weight');
		},
		initSleepChart() {
			this.initChart('sleepChart', 'sleep');
		},
		updateSleepChart() {
			this.initChart('sleepChart', 'sleep');
		},
		initWaterChart() {
			this.initChart('waterChart', 'water');
		},
		updateWaterChart() {
			this.initChart('waterChart', 'water');
		},
		initExerciseChart() {
			this.initChart('exerciseChart', 'exercise');
		},
		updateExerciseChart() {
			this.initChart('exerciseChart', 'exercise');
		},
		initChart(canvasId, type) {
			const query = uni.createSelectorQuery().in(this);
			query.select(`#${canvasId}`).boundingClientRect(data => {
				if (data) {
					this.chartWidth = data.width;
					this.chartHeight = data.height;
					const ctx = uni.createCanvasContext(canvasId, this);
					this.drawSimpleChart(ctx, type);
				}
			}).exec();
		},
		drawSimpleChart(ctx, type) {
			// 清空画布
			ctx.clearRect(0, 0, this.chartWidth, this.chartHeight);

			// 设置画布背景
			ctx.fillStyle = '#ffffff';
			ctx.fillRect(0, 0, this.chartWidth, this.chartHeight);

			// 获取数据
			let data, colors, unit;
			switch (type) {
				case 'weight':
					data = this.weightChartData[this.selectedTimeRange];
					colors = ['#007bff'];
					unit = 'kg';
					break;
				case 'sleep':
					data = this.sleepChartData[this.selectedTimeRange];
					colors = ['#9c27b0'];
					unit = 'h';
					break;
				case 'water':
					data = this.waterChartData[this.selectedTimeRange];
					colors = ['#2196f3'];
					unit = 'ml';
					break;
				case 'exercise':
					data = this.exerciseChartData[this.selectedTimeRange];
					colors = ['#ff9800'];
					unit = 'h';
					break;
			}

			const { dates } = data;

			// 检查是否有数据
			if (!dates || dates.length === 0) {
				this.drawNoDataMessage(ctx, type);
				ctx.draw();
				return;
			}

			const padding = Math.max(20, this.chartWidth * 0.08); // 动态计算边距
			const bottomPadding = Math.max(25, this.chartHeight * 0.15); // 底部留更多空间给日期
			// 恢复正常的顶部边距，不再需要为标题预留空间
			const topPadding = Math.max(20, this.chartHeight * 0.12);

			const chartArea = {
				left: padding,
				top: topPadding,
				right: this.chartWidth - padding,
				bottom: this.chartHeight - bottomPadding,
				width: this.chartWidth - 2 * padding,
				height: this.chartHeight - topPadding - bottomPadding
			};

			// 绘制日期标签
			ctx.fillStyle = '#666';
			ctx.font = `${Math.max(10, this.chartWidth * 0.03)}px Arial`;
			ctx.textAlign = 'center';
			dates.forEach((date, index) => {
				const x = chartArea.left + (index * chartArea.width) / (dates.length - 1);
				ctx.fillText(date, x, this.chartHeight - 8);
			});

			// 绘制数据
			if (type === 'weight') {
				this.drawWeightData(ctx, chartArea, data, colors, unit);
			} else {
				this.drawSingleLineData(ctx, chartArea, data, colors[0], unit, type);
			}

			ctx.draw();
		},
		drawWeightData(ctx, area, data, colors, unit) {
			const { actualWeights } = data;
			const minWeight = Math.min(...actualWeights) - 1;
			const maxWeight = Math.max(...actualWeights) + 1;
			const weightRange = maxWeight - minWeight;

			// 只绘制实际体重线
			this.drawSimpleLine(ctx, area, actualWeights, minWeight, weightRange, colors[0], unit, 'actual');
		},
		drawSingleLineData(ctx, area, data, color, unit, type) {
			let values;
			switch (type) {
				case 'sleep':
					values = data.sleepHours;
					break;
				case 'water':
					values = data.waterIntake;
					break;
				case 'exercise':
					values = data.exerciseHours;
					break;
			}

			const minValue = Math.min(...values) - (type === 'water' ? 200 : 0.5);
			const maxValue = Math.max(...values) + (type === 'water' ? 200 : 0.5);
			const valueRange = maxValue - minValue;

			this.drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit);
		},
		drawSimpleLine(ctx, area, values, minValue, valueRange, color, unit, lineType = 'single') {
			ctx.strokeStyle = color;
			ctx.lineWidth = 2;
			ctx.fillStyle = color;

			// 绘制折线
			ctx.beginPath();
			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				if (index === 0) {
					ctx.moveTo(x, y);
				} else {
					ctx.lineTo(x, y);
				}
			});
			ctx.stroke();

			// 绘制数据点和标签
			const fontSize = Math.max(8, this.chartWidth * 0.025);
			ctx.font = `${fontSize}px Arial`;
			ctx.textAlign = 'center';
			ctx.fillStyle = color;

			values.forEach((value, index) => {
				const x = area.left + (index * area.width) / (values.length - 1);
				const y = area.bottom - ((value - minValue) / valueRange) * area.height;

				// 绘制数据点
				const pointSize = Math.max(2, this.chartWidth * 0.008);
				ctx.beginPath();
				ctx.arc(x, y, pointSize, 0, 2 * Math.PI);
				ctx.fill();

				// 绘制数值标签 - 调整位置避免重叠
				ctx.fillStyle = '#333';
				const displayValue = unit === 'ml' ? value.toString() : value.toFixed(1);
				// 根据数据点位置和线条类型调整标签位置
				const labelOffset = Math.max(10, this.chartHeight * 0.05);
				let labelY;
				if (lineType === 'target') {
					labelY = y + labelOffset; // 目标线标签显示在下方
				} else if (lineType === 'actual') {
					labelY = y - labelOffset * 0.8; // 实际线标签显示在上方
				} else {
					labelY = y < area.height * 0.3 ? y + labelOffset : y - labelOffset * 0.8; // 单线图根据位置调整
				}
				ctx.fillText(displayValue + unit, x, labelY);
				ctx.fillStyle = color;
			});
		},

		drawNoDataMessage(ctx, type) {
			// 绘制无数据提示
			ctx.fillStyle = '#999';
			ctx.font = `${Math.max(16, this.chartWidth * 0.04)}px Arial`;
			ctx.textAlign = 'center';

			const centerX = this.chartWidth / 2;
			const centerY = this.chartHeight / 2;

			// 绘制图标
			ctx.font = `${Math.max(32, this.chartWidth * 0.08)}px Arial`;
			ctx.fillText('📊', centerX, centerY - 20);

			// 绘制文字
			ctx.font = `${Math.max(14, this.chartWidth * 0.035)}px Arial`;
			ctx.fillText('暂无数据', centerX, centerY + 15);

			// 根据图表类型显示不同的提示
			const typeMessages = {
				'weight': '请先记录体重数据',
				'sleep': '请先记录睡眠数据',
				'water': '请先记录饮水数据',
				'exercise': '请先记录运动数据'
			};

			ctx.font = `${Math.max(12, this.chartWidth * 0.03)}px Arial`;
			ctx.fillStyle = '#ccc';
			ctx.fillText(typeMessages[type] || '请先记录相关数据', centerX, centerY + 35);
		}
	}
}
</script>

<style lang="scss">
.container {
	padding: 20upx;
	background-color: #f0f5f9;
	/* 浅蓝色调背景 */
	min-height: 100vh;
	font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'microsoft yahei', sans-serif;
}

.header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30upx;
	padding: 20upx 0;

	.header-left {
		flex: 1;
		display: flex;
		justify-content: flex-start;

		.action-buttons {
			display: flex;
			gap: 15upx;

			.action-btn {
				display: flex;
				align-items: center;
				padding: 12upx 16upx;
				border-radius: 20upx;
				background-color: #f8f9fa;
				border: 1upx solid #e9ecef;
				transition: all 0.3s ease;

				&:active {
					background-color: #e9ecef;
					transform: scale(0.95);
				}

				&.back-btn {
					background-color: #e3f2fd;
					border-color: #2196f3;

					.btn-icon {
						color: #2196f3;
					}

					.btn-text {
						color: #2196f3;
					}

					&:active {
						background-color: #bbdefb;
					}
				}

				&.refresh-btn {
					background-color: #e8f5e8;
					border-color: #4caf50;

					.btn-icon {
						color: #4caf50;
					}

					.btn-text {
						color: #4caf50;
					}

					&:active {
						background-color: #c8e6c9;
					}
				}

				.btn-icon {
					font-size: 28upx;
					font-weight: bold;
					margin-right: 6upx;
				}

				.btn-text {
					font-size: 24upx;
					font-weight: 500;
				}
			}
		}
	}

	.header-center {
		flex: 1;
		display: flex;
		justify-content: center;

		.user-info {
			display: flex;
			align-items: center;

			.avatar {
				width: 80upx;
				height: 80upx;
				border-radius: 50%;
				margin-right: 15upx;
				border: 2upx solid #fff;
			}

			.username {
				font-size: 36upx;
				font-weight: bold;
				color: #333;
			}
		}
	}

	.header-right {
		flex: 1;
		display: flex;
		justify-content: flex-end;

		.date {
			font-size: 28upx;
			color: #666;
		}
	}
}

.summary-card,
.punch-card-section,
.chart-section {
	background-color: #fff;
	border-radius: 20upx;
	/* 更圆润的边角 */
	padding: 30upx;
	margin-bottom: 30upx;
	box-shadow: 0 8upx 24upx rgba(0, 0, 0, 0.08);
	/* 更明显的阴影 */
}

.summary-title,
.punch-card-title,
.chart-title {
	font-size: 36upx;
	/* 标题字号增大 */
	font-weight: bold;
	margin-bottom: 25upx;
	/* 标题下边距增大 */
	color: #333;
	text-align: center;
	/* 标题居中 */
}

.summary-content {
	.summary-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 25upx;

		&.single-item {
			justify-content: center;
		}

		.summary-item {
			flex: 1;
			text-align: center;
			padding: 20upx 15upx;
			background-color: #f8f9fa;
			border-radius: 12upx;
			margin: 0 8upx;
			border: 1upx solid #e9ecef;
			position: relative;
			transition: all 0.3s ease;

			&.full-width {
				max-width: 300upx;
				margin: 0 auto;
			}

			&:active {
				background-color: #e9ecef;
				transform: scale(0.98);
			}

			.item-label {
				font-size: 24upx;
				color: #666;
				margin-bottom: 8upx;
			}

			.item-value {
				font-size: 32upx;
				font-weight: bold;
				color: #333;
				position: relative;

				&.highlight {
					color: #007bff;
				}

				&.target-highlight {
					color: #e40000;
				}

				&.undefined-value {
					color: #999;
					font-style: italic;
					font-weight: normal;
				}

				.edit-icon {
					font-size: 20upx;
					margin-left: 8upx;
					opacity: 0.6;
				}
			}
		}
	}
}

.ai-summary-section {
	margin-bottom: 25upx;
	padding: 25upx;
	background-color: #f8f9ff;
	border-radius: 15upx;
	border: 1upx solid #e6f0ff;

	.ai-summary-title {
		display: flex;
		align-items: center;
		margin-bottom: 15upx;
		font-size: 28upx;
		font-weight: bold;
		color: #333;

		.ai-icon {
			font-size: 32upx;
			margin-right: 8upx;
		}
	}

	.ai-summary-content {
		.summary-text {
			font-size: 26upx;
			line-height: 1.6;
			color: #555;
			text-align: left;

			&.undefined-value {
				color: #999;
				font-style: italic;
				text-align: center;
			}
		}
	}
}

.summary-stats {
	display: flex;
	justify-content: space-around;
	margin-top: 20upx;
	padding-top: 20upx;
	border-top: 1upx solid #e9ecef;

	.stats-item {
		display: flex;
		align-items: center;
		font-size: 28upx;
		color: #666;

		.stats-icon {
			font-size: 32upx;
			margin-right: 8upx;
		}

		.highlight {
			color: #007bff;
			font-weight: bold;
			margin: 0 6upx;
		}
	}
}

.punch-card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 25upx;

	.punch-card-title-container {
		display: flex;
		flex-direction: column;
		align-items: flex-start;

		.punch-card-title {
			margin-bottom: 0;
			text-align: left;
		}

		.date-range-display {
			font-size: 26upx;
			color: #007bff;
			font-weight: 600;
			margin-top: 5upx;
			background-color: rgba(0, 123, 255, 0.1);
			padding: 4upx 12upx;
			border-radius: 12upx;
			border: 1upx solid rgba(0, 123, 255, 0.3);
		}
	}

	.time-tags {
		display: flex;
		gap: 15upx;

		.time-tag {
			padding: 8upx 20upx;
			border-radius: 20upx;
			font-size: 26upx;
			color: #666;
			background-color: #f5f5f5;
			border: 1upx solid #e0e0e0;
			transition: all 0.3s ease;
			cursor: pointer;

			&.active {
				background-color: #007bff;
				color: #ffffff;
				border-color: #007bff;
				font-weight: bold;

				/* 确保激活状态下文字始终为白色 */
				&:hover {
					background-color: #0056b3;
					color: #ffffff;
				}
			}

			/* 只在非激活状态下应用hover效果 */
			&:not(.active):hover {
				background-color: #e6f3ff;
				color: #007bff;
				border-color: #007bff;
			}
		}
	}
}

.custom-days-input {
	margin-bottom: 20upx;
	padding: 20upx;
	background-color: #f8f9fa;
	border-radius: 12upx;
	border: 1upx solid #e9ecef;

	.input-container {
		display: flex;
		align-items: center;
		gap: 12upx;

		.input-label {
			font-size: 26upx;
			color: #333;
			font-weight: 500;
			white-space: nowrap;
		}

		.days-input {
			width: 120upx;
			padding: 12upx 16upx;
			border: 1upx solid #ddd;
			border-radius: 8upx;
			font-size: 28upx;
			background-color: #fff;
			text-align: center;
		}

		.input-unit {
			font-size: 26upx;
			color: #666;
		}

		.confirm-btn {
			padding: 12upx 24upx;
			background-color: #007bff;
			color: white;
			border-radius: 8upx;
			font-size: 26upx;
			font-weight: 500;
			transition: all 0.3s ease;

			&:active {
				background-color: #0056b3;
				transform: scale(0.95);
			}
		}
	}
}

.calendar-grid {
	display: flex;
	flex-direction: column;
	gap: 10upx;

	.no-data-message {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 80upx 40upx;
		text-align: center;

		.no-data-icon {
			font-size: 80upx;
			margin-bottom: 20upx;
		}

		.no-data-text {
			font-size: 32upx;
			color: #999;
			margin-bottom: 10upx;
			font-weight: 500;
		}

		.no-data-tip {
			font-size: 24upx;
			color: #ccc;
		}
	}

	.calendar-row {
		display: flex;
		justify-content: space-around;
		gap: 8upx;

		.calendar-day {
			flex: 1;
			min-height: 80upx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: #f8f9fa;
			border-radius: 12upx;
			border: 1upx solid #e0e0e0;
			position: relative;
			transition: all 0.3s ease;

			&.today {
				background-color: #e6f3ff;
				border-color: #007bff;
				border-width: 2upx;
			}

			.day-text {
				font-size: 24upx;
				color: #333;
				font-weight: 500;
			}

			.punch-dots {
				display: flex;
				gap: 4upx;
				margin-top: 4upx;

				.punch-dot {
					width: 10upx;
					height: 10upx;
					border-radius: 50%;

					&.diet-dot {
						background-color: #4caf50;
						/* 绿色表示饮食打卡 */
					}

					&.exercise-dot {
						background-color: #f44336;
						/* 红色表示运动打卡 */
					}
				}
			}
		}
	}
}

.chart-section {
	text-align: center;

	.chart-container {
		width: 100%;
		height: 240upx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-bottom: 15upx;
		overflow: hidden;
		border: 1upx solid #e0e0e0;
		border-radius: 8upx;

		.chart-canvas {
			width: 100%;
			height: 100%;
		}
	}

	.chart-legend {
		display: flex;
		justify-content: center;
		gap: 25upx;
		margin-top: 10upx;
		margin-bottom: 5upx;

		.legend-item {
			display: flex;
			align-items: center;
			gap: 6upx;

			.legend-color {
				width: 14upx;
				height: 14upx;
				border-radius: 2upx;

				&.actual-weight {
					background-color: #007bff;
				}

				&.target-weight {
					background-color: #28a745;
				}

				&.sleep-time {
					background-color: #9c27b0;
				}

				&.water-intake {
					background-color: #2196f3;
				}

				&.exercise-time {
					background-color: #ff9800;
				}
			}

			.legend-text {
				font-size: 22upx;
				color: #666;
			}
		}
	}

	.chart-image {
		width: 100%;
		height: 450upx;
		/* 图表高度增加 */
		object-fit: contain;
	}
}
</style>
