<template>
	<view class="container">
		<image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
		<view class="header">
			<view class="avatar">
				<image :src="getUserAvatar" mode="aspectFill"></image>
			</view>
			<view class="nickname">
				<text>{{ getUserName }}</text>
			</view>
		</view>
		<view class="content">
			<text class="text-title">请输入昵称</text>
			<text class="text-sub-title">须在3-12个字符之间</text>
			<!-- <input class="input-nickname" placeholder="请输入昵称" v-model="newNickname" /> -->


			<input :clearable="false" type="nickname" class="input-nickname" :value="userName" @blur="bindblur"
				placeholder="请输入昵称" @input="bindinput" />


			<button class="btn-ok" @click="clickOk">确认</button>
		</view>
		<view class="footer" />
	</view>

</template>

<script>
import myStorage from "@/common/my-storage";
export default {
	onReady() {
		this.userInfo = myStorage.getUserInfo()
		console.log("userInfo:", this.userInfo)
		if (this.userInfo) {
			this.newNickname = this.userInfo?.vivid?.name || this.userInfo.name
			// this.userName = this.userInfo?.vivid?.name || this.userInfo.name
			this.userName = this.userInfo?.vivid?.name
		}
		let systemInfo = uni.getSystemInfoSync()
		this.height = systemInfo.windowHeight
		console.log("set-infp:", this.height, this.userInfo)
	},
	data() {
		return {
			userInfo: {},
			newNickname: '',
			height: 844,
			userName: '',
			bg_img: 'https://vivid-public.oss-cn-beijing.aliyuncs.com/bg/bg-1.jpg',

		}
	},
	methods: {
		bindblur(e) {
			this.newNickname = e.detail.value; // 获取微信昵称
			console.log("bindblur:", e.detail.value)
		},
		bindinput(e) {
			console.log("bindinput:", e.detail.value)
			this.newNickname = e.detail.value; // 获取微信昵称
		},
		clickOk() {
			if (!this.newNickname || this.newNickname == '' || this.newNickname.length < 1) {
				uni.showToast({
					title: '昵称不能为空',
					icon: 'none'
				})
				return
			}
			if (this.newNickname !== this.userInfo.vivid.name) {
				const user = myStorage.setUserInfo({
					...this.userInfo,
					name: this.newNickname,
				})
				console.log("updateUser:", this, this.userInfo)
				this.$groupx.updateUser({ name: this.newNickname, account: user.account })?.then(res => {
					console.log("updateUser res:", res)
				})
			}

			setTimeout(() => {
				uni.reLaunch({ url: '/pages/mine/set-info-other' })
			}, 1000)
		}
	}
}
</script>

<style>
.container {
	width: 100%;
	height: 100vh;
}

.background {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	z-index: -1;
}

.content {
	width: auto;
	padding: 20px;
	display: flex;
	flex-direction: column;
}



.text-title {
	width: 240rpx;
	height: 68rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 48rpx;
	color: #D5E4FF;
	line-height: 56rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.text-sub-title {
	margin-top: 20upx;
	width: 250rpx;
	height: 36rpx;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 26rpx;
	color: #c4bcbc;
	line-height: 30rpx;
	text-align: left;
	font-style: normal;
	text-transform: none;
}

.input-nickname {
	margin-top: 60upx;
	width: 660rpx;
	height: 100rpx;
	background: #D5E4FF;
	border-radius: 16rpx 16rpx 16rpx 16rpx;
	font-size: 32upx;
	background: #3333337f;
	color: #f5f5f5;
	text-align: center;
}

.btn-ok {
	margin-top: 60upx;
	color: white;

	width: 660rpx;
	height: 100rpx;
	line-height: 110rpx;
	background: linear-gradient(90deg, #74A5FF 0%, #3B7EF8 100%);
	border-radius: 16rpx 16rpx 16rpx 16rpx;
}
</style>
