<template>
	<view class="ads-container" :animation="animationData">
		<image class="ads-img" :src="ads" lazy-load=true mode="aspectFill"></image>
		<text class="countdown-text" v-if="isCountDownShow && isClickSkip === 0" @click="skipTo">{{ countDownTime
		}}</text>
		<text class="user-id-text" v-if="userObjectId">{{ userObjectId }}</text>
	</view>
</template>

<script>
import groupx from "@/api/api-groupx";
import myStorage from '@/common/my-storage';
import { getSsoClientId } from '@/common/util';
import { getAgent } from '@/common/util';
export default {
	name: "ads",
	data() {
		return {
			ads: require('@/static/result-1.png'),
			countDownTime: '',
			seconds: 3, // 增加倒计时时间，确保有足够时间显示
			timer: null,
			isCountDownShow: false,
			isClickSkip: 0,
			animationData: {}, // 添加动画数据属性
			logging: false,
			userInfo: myStorage.getUserInfo() || {},
			healthData: {},
			userObjectId: '',
			groupObjectId: '',
			page_from: '', // 用于记录跳转来源
		}
	},

	onShow() {
		console.log("=====>onShow", this.userInfo)
		this.initData()
		if (!this.userInfo?.account) {
			this.toUniLogin() // 页面显示时执行登录
		}

	},
	onReady() {
		//太早调用会无法正常存储
		myStorage.setPageFrom(this.page_from)
		if (this.userObjectId) myStorage.setUserObjectId(this.userObjectId)
		if (this.groupObjectId) myStorage.setGroupObjectId(this.groupObjectId)
	},
	onLoad(options) {
		const that = this
		console.log("=====>onLoad", options, this.userInfo)
		if (this.userInfo.account) {
			if (this.userInfo.healthData) {
				this.healthData = this.userInfo.healthData
			} else {
				groupx.getHealthSurvey().then(res => {
					console.log("获取健康调查结果", res)
					if (res.code === 200) {
						that.userInfo.healthData = res.data
						that.userInfo = myStorage.setUserInfo(that.userInfo)
					}
				})
			}
		}

		let userObjectId = options.userObjectId; // 获取 URL 中的 userId 参数
		if(!userObjectId) userObjectId = this.userInfo?.userObjectId
		if (!userObjectId && (process.env.NODE_ENV === "development")) {
			console.warn("开发环境,使用固定userObjectId")
			userObjectId = "LowuPGPYmt"
		}
		console.warn('传入的userObjectId:', userObjectId);
		// 页面右下角,使用较淡字体显示userObjectId
		this.userObjectId = userObjectId
		this.groupObjectId = options.groupObjectId; // 获取 URL 中的 groupId 参数
		this.page_from = options.from || 'weight_loss'; // 获取跳转来源

		console.log("跳转来源 page_from:", this.page_from, this.userObjectId)
	},
	methods: {
		initData() {
			this.isCountDownShow = true
			this.countDown(this.seconds)
		},
		skipTo() {
			this.clearTimer()
			let url = '/pages/index/index'
			if (this.page_from === 'health_check') {
				url = '/pages/health_check/index'
			} else if (this.page_from === 'weight_loss') {
				url = '/pages/result'
			} else if (this.userInfo.healthData) {
				url = '/pages/result'
			}
			uni.navigateTo({
				url,
				success: () => {
					// 传递登录状态到index页面
					const pages = getCurrentPages()
					const indexPage = pages[pages.length - 1]
					if (indexPage) {
						//indexPage.$vm.isFromAds = true
					}
				}
			});
		},

		// 登录请求只是获得数据,并存储到本地,不要执行UI操作
		groupxLogin(code) {
			const that = this;
			const ssoClientId = getSsoClientId();
			console.log("微信小程序登录code：", code, ssoClientId, this.userObjectId)
			groupx.wxMiniProgramLogin({
				platformName: 'GATESSO',
				agent: getAgent(),
				authData: {
					code, ssoClientId, userObjectId: this.userObjectId,
					scope: "read", tag: "wechat_miniprogram"
				}
			})?.then((res) => {
				that.logging = false
				console.info("微信小程序登录返回：", res)
				if (res.success) {
					console.log("存储UserInfo", res.account)
					that.userInfo = myStorage.setUserInfo(res)
					myStorage.setSession({ sessionId: res.sessionId, expiresAt: res.expiresAt, });
					groupx.onLogin()
					// 登录请求只是获得数据,并存储到本地,不要执行UI操作
					//that.skipTo()
					// 获取已经填写的健康调研数据
					groupx.getHealthSurvey().then(res => {
						console.log("获取健康调查结果", res)
						if (res.code === 200) {
							that.userInfo.healthData = res.data
							that.userInfo = myStorage.setUserInfo(that.userInfo)
						}
					})
				}
			}).catch(function (error) {
				that.logging = false
				console.log("微信登录失败", error)
				uni.showToast({
					title: error.code == "ERR_NETWORK" ? "网络错误" : "微信登录失败",
					icon: "none",
					duration: 5000,
				})
			})
		},

		toUniLogin() {
			const that = this
			console.log("开始执行登录流程toUniLogin")

			uni.login({
				provider: "weixin",
				onlyAuthorize: true,
				success: function (res) {
					console.log("toUniLogin获取weixin登录code：", res)
					if (res.code) {
						console.log("toUniLogin获取weixin登录code：", res.code)
						that.groupxLogin(res.code)
					}
				}
			})
		},

		// 倒计时
		countDown(times) {
			const _self = this
			// 先显示初始时间
			_self.countDownTime = `${times}s`
			_self.timer = setInterval(function () {
				times--
				// 更新显示的倒计时
				if (times >= 0) {
					_self.countDownTime = `${times}s`
				}

				// 倒计时结束
				if (times < 0) {
					_self.countDownTime = '0s'
					clearInterval(_self.timer) // 清除定时器
					_self.timer = null
					// 即使登录请求未完成,也进入下一页
					// 登录请求只是获得数据,并存储到本地,不要执行UI操作
					_self.skipTo() // 跳转到下一页

				}
			}, 1000)
		},
		clearTimer() {
			clearInterval(this.timer)
			this.timer = null
		}
	}
};
</script>
<style lang="scss" scoped>
@import '@/styles/variables.scss';

.ads-container {
	position: relative;
	height: 100vh;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: $bg-primary;
	transition: opacity 0.3s ease;
	will-change: opacity;

	>.ads-img {
		width: 100%;
		object-fit: cover;
		opacity: 0.8;
	}

	.countdown-text {
		position: fixed;
		top: 80upx;
		left: 40upx;
		width: 120upx;
		border-radius: 60upx;
		background-color: $primary-color;
		color: $text-primary;
		font-size: 32upx;
		height: 120upx;
		line-height: 120upx;
		text-align: center;
		box-shadow: $btn-shadow;
	}

	.user-id-text {
		position: fixed;
		bottom: 20upx;
		right: 20upx;
		font-size: 24upx;
		color: rgba(0, 0, 0, 0.3);
	}
}
</style>
