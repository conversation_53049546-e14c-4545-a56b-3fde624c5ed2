<template>
	<view class="form-container">
		<image class="background" :src="bg_img" lazy-load=true mode="aspectFill"></image>
		<view class="title">身体状况评估</view>
		<view class="step-indicator">
			<view class="step completed">1</view>
			<view class="step-line completed"></view>
			<view class="step active">2</view>
			<view class="step-line"></view>
			<view class="step">3</view>
		</view>
		<view class="content-section">
			<view class="tag-group">
				<view class="categories">
					<view class="category">
						<view class="category-header">
							<text class="category-title">饮食习惯与口腔状况</text>
						</view>
						<view class="button-row">
							<button v-for="(tag, index) in tags_1" :key="index"
								@click="clickBtnTag('1', index, tag)"
								:class="[tags_sel['1'].includes(index) ? 'sel-button' : 'transparent-button']">
								{{ tag }}
							</button>
						</view>
					</view>

					<view class="category">
						<view class="category-header">
							<text class="category-title">水分摄入与皮肤状况</text>
						</view>
						<view class="button-row">
							<button v-for="(tag, index) in tags_2" :key="index"
								@click="clickBtnTag('2', index, tag)"
								:class="[tags_sel['2'].includes(index) ? 'sel-button' : 'transparent-button']">
								{{ tag }}
							</button>
						</view>
					</view>

					<view class="category">
						<view class="category-header">
							<text class="category-title">饮食习惯与饥饿感</text>
						</view>
						<view class="button-row">
							<button v-for="(tag, index) in tags_3" :key="index"
								@click="clickBtnTag('3', index, tag)"
								:class="[tags_sel['3'].includes(index) ? 'sel-button' : 'transparent-button']">
								{{ tag }}
							</button>
						</view>
					</view>

					<view class="category">
						<view class="category-header">
							<text class="category-title">心理状态与压力管理</text>
						</view>
						<view class="button-row">
							<button v-for="(tag, index) in tags_5" :key="index" :id="'other-tags-' + index"
								@click="clickBtnTag('5', index, tag)"
								:class="[tags_sel['5'].includes(index) ? 'sel-button' : 'transparent-button']">
								{{ tag }}
							</button>
						</view>
					</view>
				</view>
			</view>

			<view class="input-item full-width health-condition">
				<text class="input-label">其他健康状况 (选填)</text>
				<view class="tips-text">请描述您的其他健康状况,如慢性疾病、过敏等</view>
				<textarea v-model="healthCondition" class="health-condition-input"  auto-height />
			</view>
		</view>

		<view class="footer">
			<view class="tips-text">选择您的情况,帮助我们了解您的健康状况</view>
			<button :class="[nextDisabled ? 'next-button-disabled' : 'next-button']"
				@click="clickNextStep">继续(2/3)</button>
		</view>
	</view>
</template>

<script>
import myStorage from "../../common/my-storage"

export default {
	data() {
		return {
			title: '健康科学减重',
			bg_img: require('@/static/640.png'),
			nextDisabled: true,
			tags_sel: { '1': [], '2': [], '3': [], '5': [] },
			tags_1: ['良好', '一般', '欠佳'],
			tags_2: ['充足', '一般', '不足'],
			tags_3: ['正常', '较大', '很大'],
			tags_5: ['良好', '一般', '紧张'],
			healthCondition: '',
			userInfo: myStorage.getUserInfo()
		}
	},
	onReady() {
		console.log("body-condition onReady")
		this.getBodyCondition()
	},
	methods: {
		// 计算选择的标签数量
		countSelTags() {
			let totalElements = 0;
			for (const key in this.tags_sel) {
				if (Array.isArray(this.tags_sel[key])) {
					totalElements += this.tags_sel[key].length;
				}
			}
			return totalElements;
		},

		// 点击标签按钮
		clickBtnTag(type, index) {
			console.log("clickBtnTag:", type, index)
			const isExist = this.tags_sel[type].indexOf(index);
			// 取消选择
			if (isExist !== -1) {
				this.tags_sel[type].splice(isExist, 1);
			}
			else {
				// 标记选择
				this.tags_sel[type]=[index]
			}

			// 更新下一步按钮状态
			this.nextDisabled = this.countSelTags() < 1;
			return true
		},

		// 获取已保存的身体状况数据
		getBodyCondition() {
			// 检查是否已有保存的数据
			const user = myStorage.getUserInfo();
			if (user && user.healthData) {
				// 恢复其他健康状况
				if (user.healthData.otherConditions) {
					this.healthCondition = user.healthData.otherConditions;
				}

				// 恢复身体状况评估
				if (user.healthData.bodyCondition) {
					const bodyCondition = user.healthData.bodyCondition;

					// 恢复之前的选择
					for (const categoryName in bodyCondition) {
						const value = bodyCondition[categoryName];
						let key = '';
						let index = -1;

						switch(categoryName) {
							case '饮食习惯与口腔状况':
								key = '1';
								index = this.tags_1.indexOf(value);
								break;
							case '水分摄入与皮肤状况':
								key = '2';
								index = this.tags_2.indexOf(value);
								break;
							case '饮食习惯与饥饿感':
								key = '3';
								index = this.tags_3.indexOf(value);
								break;
							case '心理状态与压力管理':
								key = '5';
								index = this.tags_5.indexOf(value);
								break;
						}

						if (key && index >= 0) {
							this.tags_sel[key] = [index];
						}
					}

					// 如果有选择,启用下一步按钮
					if (this.countSelTags() > 0) {
						this.nextDisabled = false;
					}
				}
			}
		},

		// 点击下一步按钮
		clickNextStep() {
			console.log("clickNextStep:", this.countSelTags());

			if (this.countSelTags() < 1) {
				uni.showToast({
					title: '请至少选择一项身体状况',
					icon: 'none'
				});
				return;
			}

			// 保存用户选择的身体状况数据
			let healthData = {};

			// 保存其他健康状况
			if (this.healthCondition) {
				healthData.otherConditions = this.healthCondition;
			}

			// 保存身体状况评估
			healthData.bodyCondition = {};
			for (const key in this.tags_sel) {
				if (this.tags_sel[key].length > 0) {
					const index = this.tags_sel[key][0];
					let categoryName = '';
					switch(key) {
						case '1': categoryName = '饮食习惯与口腔状况'; break;
						case '2': categoryName = '水分摄入与皮肤状况'; break;
						case '3': categoryName = '饮食习惯与饥饿感'; break;
						case '5': categoryName = '心理状态与压力管理'; break;
					}

					let tagValue = '';
					switch(key) {
						case '1': tagValue = this.tags_1[index]; break;
						case '2': tagValue = this.tags_2[index]; break;
						case '3': tagValue = this.tags_3[index]; break;
						case '5': tagValue = this.tags_5[index]; break;
					}

					healthData.bodyCondition[categoryName] = tagValue;
				}
			}

			// 将数据存储到本地
			let user = myStorage.getUserInfo();
			if (!user.healthData) {
				user.healthData = {};
			}

			// 合并数据
			user.healthData = {...user.healthData, ...healthData};
			myStorage.setUserInfo(user);

			// 跳转到下一个页面
			uni.navigateTo({ url: '/pages/mine/set-info-other' });
		}
	}
}
</script>

<style lang="scss" scoped>
@import '@/styles/common-form.scss';

// 自定义样式

.tag-group {
	padding: 0;

	.categories {
		display: flex;
		flex-direction: column;
		overflow-x: hidden;

		.category {
			margin-bottom: 10rpx;
			background-color: transparent;

			.category-header {
				margin-top: 10rpx;
				display: flex;
				width: 100%;
				align-items: center;

				.category-title {
					font-size: 30rpx;
					margin-bottom: 10rpx;
					white-space: nowrap;
					color: $text-primary;
					font-weight: 500;
					letter-spacing: 1rpx;
					z-index: 2;
				}
			}

			.button-row {
				display: flex;
				justify-content: space-between;
				width: 100%;
				margin-bottom: 10rpx;
				button {
					flex: 1;
					margin: 0 5rpx;
					outline: none;
					-webkit-appearance: none;
					-webkit-tap-highlight-color: transparent;
					&:after {
						border: none;
					}

					&:first-child {
						margin-left: 0;
					}

					&:last-child {
						margin-right: 0;
					}
				}

				.transparent-button {
					height: 70rpx;
					line-height: 70rpx;
					font-size: 26rpx;
					text-align: center;
					color: rgba(255, 255, 255, 0.9);
					background-color: rgba(255, 255, 255, 0.1);
					border: 1px solid rgba(255, 255, 255, 0.2);
					padding: 0rpx 15rpx;
					border-radius: 35rpx;
					z-index: 2;
					border: 1px solid rgba(255, 255, 255, 0.2);
					outline: none; // 移除轮廓
					-webkit-appearance: none; // 秮除 WebKit 默认样式
					-webkit-tap-highlight-color: transparent; // 移除点击高亮
					&:after {
						border: none; // 移除小程序按钮默认边框
					}
				}

				.sel-button {
					height: 70rpx;
					line-height: 70rpx;
					font-size: 26rpx;
					text-align: center;
					color: $primary-color;
					background-color: $text-primary;
					border: none;
					padding: 0rpx 15rpx;
					border-radius: 35rpx;
					box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
					z-index: 2;
					border: none;
					outline: none;
					-webkit-appearance: none;
					-webkit-tap-highlight-color: transparent;
					&:after {
						border: none;
					}
				}
			}
		}
	}
}

.health-condition {
	margin-bottom: 30rpx;

	.health-condition-input {
		width: 100%;
		min-height: 140rpx;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: 8rpx;
		padding: 15rpx;
		color: $text-primary;
		text-align: left;
		box-sizing: border-box;
		font-size: 28rpx;
		line-height: 1.5;

		&::placeholder {
			color: rgba(255, 255, 0, 0.6); /* 黄色提示文字 */
		}
	}
}
</style>