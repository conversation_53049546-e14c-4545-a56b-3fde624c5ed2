import { ISessionInfo, IUserInfo } from "./type"

class MyStorage {
  isSessValid = false

  isLogin() {
    if (this.isSessValid) return true

    const sess = uni.getStorageSync("session")
    this.isSessValid = sess && sess.sessionId

    return this.isSessValid
  }
  getPageFrom() {
    return uni.getStorageSync("pageFrom")
  }
  setPageFrom(pageFrom: string) {
    uni.setStorageSync("pageFrom", pageFrom)
  }
  getUserObjectId() { uni.getStorageSync("userObjectId") }
  setUserObjectId(userObjectId: string) { uni.setStorageSync("userObjectId", userObjectId) }
  getGroupObjectId(){return uni.getStorageSync("groupObjectId")}
  setGroupObjectId(groupObjectId:string){uni.setStorageSync("groupObjectId", groupObjectId)}
  getUserInfo(): IUserInfo {
    return uni.getStorageSync("userInfo") || {}
  }
  setUserInfo(userInfo: Partial<IUserInfo>) {
    if (userInfo && userInfo.account) {
      this.isSessValid = true
    }
    const existingUserInfo: IUserInfo = this.getUserInfo()
    // 合并数据
    Object.assign(existingUserInfo, userInfo)
    //console.log("setUserInfo", existingUserInfo);
    uni.setStorageSync("userInfo", existingUserInfo)
    // 全局变量
    return existingUserInfo
  }
  clearUserInfo() {
    console.warn("clearUserInfo")
    uni.removeStorageSync("userInfo")
    this.isSessValid = false
  }

  getSession(): ISessionInfo {
    return uni.getStorageSync("session") || {}
  }
  setSession(session: ISessionInfo) {
    //console.log("setSession:", session);
    uni.setStorageSync("session", session as ISessionInfo)
    if (session && session.sessionId) {
      this.isSessValid = true
    }
    return session
  }
  clearSession() {
    console.warn("clearSession")
    uni.removeStorageSync("session")
    this.isSessValid = false
  }

  setTabIndex(index: number) {
    uni.setStorageSync("tabIndex", index)
  }
  getTabIndex() {
    return uni.getStorageSync("tabIndex") || 0
  }

  // 保存聊天会话(先按照时间排序)
  saveChatSession(cid: string, msg: any[]) {
    // 过滤数据
    const filteredMsg = msg.filter((obj, index, arr) => {
      return arr.findIndex((t) => t.objectId === obj.objectId) === index
    })
    // 排序
    // console.log(filteredMsg);
    const sortedMsg = msg.sort(function (a, b) {
      return new Date(a.updateTime).getTime() - new Date(b.updateTime).getTime()
    })
    console.log("saveChatSession:", sortedMsg)
    uni.setStorageSync(`chat_session_${cid}`, sortedMsg)
    return true
  }
  // 获取聊天会话
  getChatSession(cid: string) {
    return uni.getStorageSync(`chat_session_${cid}`)
  }
  clearChatSession(cid: string) {
    uni.removeStorageSync(`chat_session_${cid}`)
    return true
  }

  getRegionData() {
    const data = uni.getStorageSync("regionData")
    // console.log("getRegionData:", data);
    return data?.data
  }
  setRegionData(data: any) {
    // console.log("setRegionData status:", data.status);
    //console.log("regionData data_version:", data.data_version);
    if (data.status !== 0) {
      //   console.log("setRegionData error:", data.message);
      return undefined
    }
    const root = data.result[0].map((prov: any) => {
      const provId = prov.id
      const str1 = provId.substring(0, 3)
      const children = data.result[1]
        .filter((city: any) => {
          return str1 === city.id.substring(0, 3)
        })
        .map((city: any) => {
          const cityId = city.id
          const str1 = cityId.substring(0, 4)
          const children2 = data.result[2]
            .filter((area: any) => {
              return str1 === area.id.substring(0, 4)
            })
            .map((area: any) => {
              const areaId = area.id
              return {
                label: area.fullname,
                id: areaId,
                parent_id: cityId,
              }
            })
          return {
            label: city.name,
            id: cityId,
            parent_id: provId,
            children: children2,
          }
        })
      return {
        label: prov.name,
        id: provId,
        children,
      }
    })
    // console.log("setRegionData result:", root);
    uni.setStorageSync("regionData", {
      data_version: data.data_version,
      data: root,
    })
    //  console.log("setRegionData end");
    return root
  }
}

export default new MyStorage()
